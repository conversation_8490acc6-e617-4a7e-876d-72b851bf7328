"""
Business Unit Adapter - Adaptador para Suporte a Unidades de Negócio
Permite que o código existente funcione com diferentes unidades de negócio.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from salesforce_integration.core.base_dag_factory import DagRegistry

class BusinessUnitAdapter:
    """Adaptador para execução específica por unidade de negócio"""
    
    def __init__(self, business_unit: Optional[str] = None):
        self.business_unit = business_unit or self._detect_business_unit()
        self.logger = logging.getLogger(__name__)
        
        # Carrega configuração específica da unidade
        self._load_business_unit_config()
    
    def _detect_business_unit(self) -> str:
        """Detecta unidade de negócio baseada no contexto de execução"""
        # Verifica variável de ambiente
        env_unit = os.getenv('BUSINESS_UNIT')
        if env_unit:
            return env_unit.lower()
        
        # Verifica contexto do Airflow se disponível
        try:
            from airflow.models import Variable
            airflow_unit = Variable.get('current_business_unit', default_var=None)
            if airflow_unit:
                return airflow_unit.lower()
        except:
            pass
        
        # Default para consórcio (mantém compatibilidade)
        return 'consorcio'
    
    def _load_business_unit_config(self):
        """Carrega configuração específica da unidade de negócio"""
        try:
            if self.business_unit == 'consorcio':
                from salesforce_integration.business_units.consorcio.config import (
                    DAG_CONSORCIO_CONFIG,
                    SALESFORCE_CONSORCIO_CONFIG,
                    DATA_EXTENSIONS_CONSORCIO,
                    SOURCES_CONSORCIO
                )
                self.dag_config = DAG_CONSORCIO_CONFIG
                self.salesforce_config = SALESFORCE_CONSORCIO_CONFIG
                self.data_extensions = DATA_EXTENSIONS_CONSORCIO
                self.sources = SOURCES_CONSORCIO
                
            elif self.business_unit == 'seguros':
                from salesforce_integration.business_units.seguros.config import (
                    DAG_SEGUROS_CONFIG,
                    SALESFORCE_SEGUROS_CONFIG,
                    DATA_EXTENSIONS_SEGUROS,
                    SOURCES_SEGUROS
                )
                self.dag_config = DAG_SEGUROS_CONFIG
                self.salesforce_config = SALESFORCE_SEGUROS_CONFIG
                self.data_extensions = DATA_EXTENSIONS_SEGUROS
                self.sources = SOURCES_SEGUROS
                
            else:
                raise ValueError(f"Unidade de negócio não suportada: {self.business_unit}")
                
            self.logger.info(f"✅ Configuração carregada para unidade: {self.business_unit}")
            
        except ImportError as e:
            self.logger.error(f"❌ Erro ao carregar configuração para {self.business_unit}: {e}")
            # Fallback para configuração padrão
            from salesforce_integration.config import SALESFORCE_CONFIG, DATA_EXTENSIONS
            self.salesforce_config = SALESFORCE_CONFIG
            self.data_extensions = DATA_EXTENSIONS
            self.sources = ['newcon', 'rdstation', 'orbbits']  # Default consórcio
    
    def get_filtered_sources(self) -> List[str]:
        """Retorna fontes filtradas para a unidade de negócio"""
        return self.sources
    
    def get_salesforce_config(self) -> Dict[str, Any]:
        """Retorna configuração Salesforce específica da unidade"""
        return self.salesforce_config
    
    def get_data_extensions(self) -> Dict[str, Any]:
        """Retorna Data Extensions específicas da unidade"""
        return self.data_extensions
    
    def filter_extraction_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Filtra dados de extração baseado na unidade de negócio"""
        if self.business_unit == 'seguros':
            # Para seguros, manter apenas dados do Quiver
            filtered_data = {}
            for key, value in data.items():
                if key.startswith('quiver_'):
                    filtered_data[key] = value
            
            self.logger.info(f"Dados filtrados para seguros: {list(filtered_data.keys())}")
            return filtered_data
            
        elif self.business_unit == 'consorcio':
            # Para consórcio, excluir dados do Quiver
            filtered_data = {}
            for key, value in data.items():
                if not key.startswith('quiver_'):
                    filtered_data[key] = value
            
            self.logger.info(f"Dados filtrados para consórcio: {list(filtered_data.keys())}")
            return filtered_data
        
        # Retorna todos os dados se não conseguir filtrar
        return data
    
    def get_output_prefix(self) -> str:
        """Retorna prefixo para arquivos de saída específicos da unidade"""
        return f"{self.business_unit}_"
    
    def get_log_context(self) -> Dict[str, str]:
        """Retorna contexto de log específico da unidade"""
        return {
            'business_unit': self.business_unit,
            'sources': ','.join(self.sources),
            'data_extensions': ','.join(self.data_extensions.keys())
        }

# =============================================================================
# FUNÇÕES ADAPTADORAS PARA AIRFLOW
# =============================================================================

def get_business_unit_from_context(**context) -> str:
    """Extrai unidade de negócio do contexto do Airflow"""
    # Verifica kwargs primeiro
    if 'business_unit' in context:
        return context['business_unit']
    
    # Verifica DAG ID
    dag_run = context.get('dag_run')
    if dag_run and dag_run.dag_id:
        if 'CONSORCIO' in dag_run.dag_id.upper():
            return 'consorcio'
        elif 'SEGUROS' in dag_run.dag_id.upper():
            return 'seguros'
    
    # Default
    return 'consorcio'

def create_business_unit_adapter(**context) -> BusinessUnitAdapter:
    """Cria adaptador baseado no contexto do Airflow"""
    business_unit = get_business_unit_from_context(**context)
    return BusinessUnitAdapter(business_unit)

# =============================================================================
# WRAPPER FUNCTIONS PARA COMPATIBILIDADE COM ETL_MAIN
# =============================================================================

def wrap_etl_function_with_business_unit(original_function):
    """Wrapper que adiciona suporte a unidades de negócio a funções existentes"""
    def wrapper(**context):
        # Cria adaptador
        adapter = create_business_unit_adapter(**context)
        
        # Adiciona adaptador ao contexto
        context['business_unit_adapter'] = adapter
        context['business_unit'] = adapter.business_unit
        
        # Chama função original
        return original_function(**context)
    
    return wrapper

# =============================================================================
# FUNÇÕES PARA MONKEYPATCHING
# =============================================================================

def patch_config_for_business_unit(adapter: BusinessUnitAdapter):
    """Aplica configurações específicas da unidade de negócio globalmente"""
    import salesforce_integration.config as config_module
    
    # Substitui configurações globais
    config_module.SALESFORCE_CONFIG = adapter.get_salesforce_config()
    config_module.DATA_EXTENSIONS = adapter.get_data_extensions()
    
    # Log da alteração
    logging.getLogger(__name__).info(
        f"🔧 Configurações globais aplicadas para: {adapter.business_unit}"
    )

def restore_original_config():
    """Restaura configurações originais (para testes)"""
    import importlib
    import salesforce_integration.config as config_module
    importlib.reload(config_module)