-- =====================================================
-- CONSULTA: Quiver Propostas
-- DESCRIÇÃO: Extrai dados de propostas do sistema Quiver
-- FONTE: <PERSON><PERSON>ui<PERSON> (SQL Server)
-- =====================================================

-- Quiver Propostas
select 
	'Propostas' end_point
	,t5.nome as Divisao
	,t2.Nome
	,t1.Proposta IdProposta
	,T1.Documento
	,right(replace(replace(replace(t2.cgc_cpf,'.',''),'-',''),'/',''),14) cnpj_cpf
	,t2.E_mail email
	,t1.Produto id_produto
	,t3.Nome as produto
	,t1.Data_proposta dt_proposta
	,t1.Data_alteracao dt_ultimaModificacao
	,null dt_fechamento
	,Termino_vigencia dt_validade
	,case t1.Situacao 
		when 1 then 'Ativa'
		when 2 then 'Cancelada'
		when 3 then 'Suspensa'
		when 5 then 'Renovadas'
		when 6 then 'Vencidas'
		when 7 then 'Perda Total'
		Else null
	end Situacao
	,null Status_proposta
	,t6.Descricao Tipo_pagamento
	,t1.Premio_liquido Valor
	,null Cod_promocional
	,null link_boleto
	,null Cod_pix
	,null link_cartao
	,null Data_validadae_proposta
from Tabela_Documentos t1 (nolock)
inner join Tabela_Clientes t2 (nolock) ON t1.Cliente = t2.Cliente  
inner join Tabela_Produtos t3 (nolock) on t1.Produto = t3.Produto
inner join Tabela_DocsRepasses t4 (nolock) ON t1.Documento = t4.Documento AND t1.Alteracao = t4.Alteracao
inner join Tabela_Divisoes t5 (nolock) ON t4.Divisao = t5.Divisao  
left join Tabela_MeiosPagto t6 on t6.Meio_pagto = t1.Meio_pagto
where isnull(t2.E_mail,'') <> '' 
and cast(Termino_vigencia as date) >= cast(getdate() as date)
 and t1.Situacao = 1
 AND t4.Nivel = 1
 order by t2.Nome