"""
ETL Consolidado - Extração de Dados
Todas as extrações de dados consolidadas com rate limiting, retry automático e paginação.
"""

import time
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List
from threading import Lock
from rdstation.crm import CRMClient
from salesforce_integration.config import *
from salesforce_integration.database_connections import get_database_connection
from salesforce_integration.utils import (
    retry_decorator,
    ProgressTracker,
    DataQualityChecker,
    setup_logging
)
from salesforce_integration.sql_loader import SQLLoader

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = setup_logging()

# =============================================================================
# CLASSE PRINCIPAL DE EXTRAÇÃO
# =============================================================================

class DataExtractor:
    """Classe principal para extrair dados de todas as fontes"""
    
    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.test_sample = test_sample
        self.extraction_stats = {
            'start_time': None,
            'end_time': None,
            'total_records': 0,
            'sources': {}
        }
        self._rate_limit_lock = Lock()
        self._last_rdstation_request = 0

        if test_sample:
            self.logger.info(f"Modo teste ativado: limitando consultas a {test_sample} registros")
        
    def extract_all_sources(self, sources: List[str] = None) -> Dict[str, pd.DataFrame]:
        """Extrai dados de todas as fontes ou fontes específicas"""
        self.extraction_stats['start_time'] = datetime.now()
        
        if sources is None:
            sources = ['newcon', 'rdstation', 'orbbits', 'quiver']
        
        self.logger.info(f"Iniciando extração de dados das fontes: {', '.join(sources)}")
        
        results = {}
        
        # Extração sequencial para evitar sobrecarga
        for source in sources:
            try:
                self.logger.info(f"Extraindo dados da fonte: {source}")
                
                if source == 'newcon':
                    results.update(self._extract_newcon_all())
                elif source == 'rdstation':
                    results['rdstation_leads'] = self.extract_rdstation_leads()
                elif source == 'orbbits':
                    results.update(self._extract_orbbits_all())
                elif source == 'quiver':
                    results.update(self._extract_quiver_all())
                
                self.logger.info(f"✅ Extração de {source} concluída com sucesso")
                
            except Exception as e:
                self.logger.error(f"❌ Erro na extração de {source}: {e}")
                # Continua com outras fontes em caso de erro
                continue
        
        self.extraction_stats['end_time'] = datetime.now()
        self.extraction_stats['total_records'] = sum(
            len(df) for df in results.values() if isinstance(df, pd.DataFrame)
        )
        
        self._log_extraction_summary(results)
        
        return results

    # Métodos de extração específicos para compatibilidade
    def extract_newcon_clients(self) -> pd.DataFrame:
        """Extrai clientes do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_clients()

    def extract_newcon_leads(self) -> pd.DataFrame:
        """Extrai leads do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_leads()

    def extract_newcon_products(self) -> pd.DataFrame:
        """Extrai produtos do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_products()

    def extract_newcon_proposals(self) -> pd.DataFrame:
        """Extrai propostas do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_proposals()

    def extract_rdstation_leads(self) -> pd.DataFrame:
        """Extrai leads do RD Station"""
        extractor = RDStationExtractor(test_sample=self.test_sample)
        return extractor.extract_leads()

    def extract_orbbits_origin(self) -> pd.DataFrame:
        """Extrai dados de origem do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_origin()

    def extract_orbbits_payments(self) -> pd.DataFrame:
        """Extrai dados de pagamentos do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_payments()

    def extract_orbbits_sales(self) -> pd.DataFrame:
        """Extrai dados de vendas do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_sales()

    def extract_orbbits_prices(self) -> pd.DataFrame:
        """Extrai dados de preços do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_prices()

    def extract_orbbits_proposals(self) -> pd.DataFrame:
        """Extrai dados de propostas do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_proposals()

    def extract_quiver_clients(self) -> pd.DataFrame:
        """Extrai dados de clientes do Quiver"""
        extractor = QuiverExtractor(test_sample=self.test_sample)
        return extractor.extract_clients()

    def extract_quiver_leads(self) -> pd.DataFrame:
        """Extrai dados de leads do Quiver"""
        extractor = QuiverExtractor(test_sample=self.test_sample)
        return extractor.extract_leads()

    def extract_quiver_products(self) -> pd.DataFrame:
        """Extrai dados de produtos do Quiver"""
        extractor = QuiverExtractor(test_sample=self.test_sample)
        return extractor.extract_products()

    def extract_quiver_proposals(self) -> pd.DataFrame:
        """Extrai dados de propostas do Quiver"""
        extractor = QuiverExtractor(test_sample=self.test_sample)
        return extractor.extract_proposals()

    def _extract_newcon_all(self) -> Dict[str, pd.DataFrame]:
        """Extrai todos os dados do NewCon"""
        results = {}
        
        # Extrai cada tabela do NewCon
        extractors = {
            'newcon_clients': self.extract_newcon_clients,
            'newcon_leads': self.extract_newcon_leads,
            'newcon_products': self.extract_newcon_products,
            'newcon_proposals': self.extract_newcon_proposals
        }
        
        for key, extractor in extractors.items():
            try:
                results[key] = extractor()
                self.logger.info(f"✅ {key}: {len(results[key])} registros extraídos")
            except Exception as e:
                self.logger.error(f"❌ Erro em {key}: {e}")
                results[key] = pd.DataFrame()  # DataFrame vazio em caso de erro
        
        return results
    
    def _extract_orbbits_all(self) -> Dict[str, pd.DataFrame]:
        """Extrai todos os dados do Orbbits"""
        results = {}
        
        # Extrai cada tabela do Orbbits
        extractors = {
            'orbbits_origin': self.extract_orbbits_origin,
            'orbbits_payments': self.extract_orbbits_payments,
            'orbbits_sales': self.extract_orbbits_sales,
            'orbbits_prices': self.extract_orbbits_prices,
            'orbbits_proposals': self.extract_orbbits_proposals
        }
        
        for key, extractor in extractors.items():
            try:
                results[key] = extractor()
                self.logger.info(f"✅ {key}: {len(results[key])} registros extraídos")
            except Exception as e:
                self.logger.error(f"❌ Erro em {key}: {e}")
                results[key] = pd.DataFrame()  # DataFrame vazio em caso de erro
        
        return results

    def _extract_quiver_all(self) -> Dict[str, pd.DataFrame]:
        """Extrai todos os dados do Quiver"""
        results = {}

        # Extrai cada tabela do Quiver
        extractors = {
            'quiver_clients': self.extract_quiver_clients,
            'quiver_leads': self.extract_quiver_leads,
            'quiver_products': self.extract_quiver_products,
            'quiver_proposals': self.extract_quiver_proposals
        }

        for key, extractor in extractors.items():
            try:
                results[key] = extractor()
                self.logger.info(f"✅ {key}: {len(results[key])} registros extraídos")
            except Exception as e:
                self.logger.error(f"❌ Erro em {key}: {e}")
                results[key] = pd.DataFrame()  # DataFrame vazio em caso de erro

        return results

    def _log_extraction_summary(self, results: Dict[str, pd.DataFrame]):
        """Registra resumo da extração"""
        duration = self.extraction_stats['end_time'] - self.extraction_stats['start_time']
        
        self.logger.info("=" * 60)
        self.logger.info("RESUMO DA EXTRAÇÃO")
        self.logger.info("=" * 60)
        self.logger.info(f"Duração total: {duration}")
        self.logger.info(f"Total de registros: {self.extraction_stats['total_records']:,}")
        
        for source, df in results.items():
            if isinstance(df, pd.DataFrame):
                self.logger.info(f"- {source}: {len(df):,} registros")
        
        self.logger.info("=" * 60)

# =============================================================================
# EXTRAÇÃO NEWCON (SQL SERVER)
# =============================================================================

class NewConExtractor:
    """Extrator especializado para NewCon"""

    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.db_name = 'newcon'
        self.test_sample = test_sample

        # Usar SQLLoader para carregar consultas
        from salesforce_integration.sql_loader import sql_loader
        self.sql_loader = sql_loader
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_clients(self) -> pd.DataFrame:
        """Extrai clientes do NewCon usando SQLLoader"""
        self.logger.info("Extraindo clientes do NewCon...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('newcon', 'clientes')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)

                # Processa dados
                if 'id_documento' in df.columns:
                    df['id_documento'] = df['id_documento'].astype(str)

                self.logger.info(f"✅ {len(df)} clientes extraídos do NewCon")
                return df

        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair clientes do NewCon: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_leads(self) -> pd.DataFrame:
        """Extrai leads do NewCon usando SQLLoader"""
        self.logger.info("Extraindo leads do NewCon...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('newcon', 'leads')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)

                # Processa dados
                if 'id_documento' in df.columns:
                    df['id_documento'] = df['id_documento'].astype(str)

                self.logger.info(f"✅ {len(df)} leads extraídos do NewCon")
                return df

        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair leads do NewCon: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_products(self) -> pd.DataFrame:
        """Extrai produtos do NewCon usando SQLLoader"""
        self.logger.info("Extraindo produtos do NewCon...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('newcon', 'produtos')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)

                self.logger.info(f"✅ {len(df)} produtos extraídos do NewCon")
                return df

        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair produtos do NewCon: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_proposals(self) -> pd.DataFrame:
        """Extrai propostas do NewCon usando SQLLoader"""
        self.logger.info("Extraindo propostas do NewCon...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('newcon', 'propostas')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)

                # Processa dados
                if 'id_documento' in df.columns:
                    df['id_documento'] = df['id_documento'].astype(str)

                self.logger.info(f"✅ {len(df)} propostas extraídas do NewCon")
                return df

        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair propostas do NewCon: {e}")
            raise

# =============================================================================
# ORBBITS EXTRACTOR
# =============================================================================

class OrbbitsExtractor:
    """Extrator especializado para Orbbits (MySQL)"""

    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.db_name = 'orbbits'
        self.test_sample = test_sample

        # Usar SQLLoader para carregar consultas
        from salesforce_integration.sql_loader import sql_loader
        self.sql_loader = sql_loader

    @retry_decorator(max_attempts=3, delay=2)
    def extract_origin(self) -> pd.DataFrame:
        """Extrai dados de origem do Orbbits usando SQLLoader"""
        self.logger.info("Extraindo dados de origem do Orbbits...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('orbbits', 'origin')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            self.logger.info("[Orbbits][origin] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][origin] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][origin] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Orbbits][origin] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de origem do Orbbits: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_payments(self) -> pd.DataFrame:
        """Extrai dados de pagamentos do Orbbits usando SQLLoader"""
        self.logger.info("Extraindo dados de pagamentos do Orbbits...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('orbbits', 'payments')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            self.logger.info("[Orbbits][payments] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][payments] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][payments] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Orbbits][payments] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de pagamentos do Orbbits: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_sales(self) -> pd.DataFrame:
        """Extrai dados de vendas do Orbbits usando SQLLoader"""
        self.logger.info("Extraindo dados de vendas do Orbbits...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('orbbits', 'sales')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            self.logger.info("[Orbbits][sales] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][sales] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][sales] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Orbbits][sales] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de vendas do Orbbits: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_prices(self) -> pd.DataFrame:
        """Extrai dados de preços do Orbbits usando SQLLoader"""
        self.logger.info("Extraindo dados de preços do Orbbits...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('orbbits', 'prices')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            self.logger.info("[Orbbits][prices] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][prices] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][prices] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Orbbits][prices] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de preços do Orbbits: {e}")
            raise

# =============================================================================
# RDSTATION EXTRACTOR
# =============================================================================

class RDStationExtractor:
    """Extrator especializado para RD Station"""
    
    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.test_sample = test_sample
        self.client = CRMClient(RDSTATION_CONFIG['token'])
        self.rate_limiter = RateLimiter(
            requests_per_minute=RDSTATION_CONFIG['rate_limit'],
            requests_per_second=RDSTATION_CONFIG['rate_limit_per_second']
        )
        
        # Mapeamento de campos customizados
        self.contact_fields = {
            '645578c328054b000f1b92cb': 'Estado',
            '6458fe2dfbe3dd0010a9f827': 'CPF',
            '6458feb9f63eb100129aa839': 'Gênero',
            '6458fed9328d400012d16444': 'Data de Nascimento',
            '6458ffa2edc3a90012b9a746': 'CEP',
            '645aa3276f5a2d000f32833d': 'Formulário de Simulacao',
            '645aa33f841efb00114c456f': 'O quanto voce conhece sobre consorcio?',
            '645aa35f20053c000f868796': 'Em quanto tempo pretende conquistar?',
            '67d4177b9d1a1a00147a4138': 'Telefone',
            '643fff689586e30019910a55': 'ValorSimulacao',
            '685b0396ed040b0019bf4c05': 'Valor do Bem',
            '685d6049dfc06d001edb357a': 'Cidade',
        }
        
        self.deal_fields = {
            '645cf919a66d5900118aa142': 'UTM_Source',
            '685d500574f982001ad79e8e': 'UTM_Medium',
            '685d500f9eacf0001af3d988': 'UTM_Term',
            '685d5022c9346f001e54cce7': 'UTM_Content',
            '6439494394d94a000dece78f': 'ID_Campanha',
            '685d505133d4ac001f45b475': 'Ponto_de_Venda',
            '685d612444467e001b80e87a': 'Plano_de_venda',
            '685d5af98c332d0014329beb': 'Valor_do_bem_deal',
            '643fff20fa5355001200c1d1': 'Simulacao_em_Parcela',
            '6671d5da10015c0023958b63': 'CPF_CNPJ_Deal',
        }
    
    @retry_decorator(max_attempts=3, delay=5)
    def extract_leads(self) -> pd.DataFrame:
        """Extrai leads do RD Station com múltiplas estratégias para garantir extração completa"""
        self.logger.info("Extraindo leads do RD Station...")

        # Primeiro carrega mapeamento de deals para vendedores
        deals_users_map, deals_data_map = self._load_deals_mapping()

        # ESTRATÉGIA OTIMIZADA: Extrai apenas os registros mais recentes (9.800 máximo)
        self.logger.info("🚀 Extraindo registros mais recentes do RD Station (ordenação decrescente por ID)")
        self.logger.info("📊 Limite máximo: 9.800 registros (49 páginas × 200 registros)")

        final_contacts = self._extract_most_recent_contacts(deals_users_map, deals_data_map)
        
        # Converte para DataFrame
        df = pd.DataFrame(final_contacts)

        # Deduplicação por ID do contato (evita duplicatas entre períodos subdivididos)
        if not df.empty and 'contact_id' in df.columns:
            initial_count = len(df)
            df = df.drop_duplicates(subset=['contact_id'], keep='first')
            final_count = len(df)
            if initial_count != final_count:
                self.logger.info(f"🔄 Deduplicação: {initial_count - final_count} duplicatas removidas ({final_count} únicos)")

        # Validação de qualidade
        self._validate_rdstation_data(df)
        
        # Validação de completeness
        self._validate_extraction_completeness(len(final_contacts))

        self.logger.info(f"✅ {len(df)} leads únicos extraídos do RD Station")
        return df

    def _extract_with_smart_pagination(self, deals_users_map: dict, deals_data_map: dict) -> List[dict]:
        """Extrai contatos usando paginação inteligente com ordenação por ID para contornar limite de 10.000"""
        self.logger.info("Usando estratégia de paginação inteligente com ordenação...")

        all_contacts = []
        page = 1
        max_safe_pages = RDSTATION_CONFIG['max_offset_limit'] // RDSTATION_CONFIG['page_size']

        # Ajusta max_pages se test_sample estiver definido
        if self.test_sample:
            max_safe_pages = min(max_safe_pages, (self.test_sample // RDSTATION_CONFIG['page_size']) + 1)

        tracker = ProgressTracker(0, "Extraindo leads RD Station com paginação inteligente")

        # Estratégia: usar ordenação por ID para garantir consistência
        order_field = 'id'  # Ordena por ID para consistência
        direction = 'asc'   # Crescente para pegar do mais antigo ao mais novo

        self.logger.info(f"Configuração: ordenação por {order_field} ({direction}), limite seguro de {max_safe_pages} páginas")

        while page <= max_safe_pages:
            try:
                # Rate limiting
                self.rate_limiter.wait_if_needed()

                # Calcula offset atual
                current_offset = (page - 1) * RDSTATION_CONFIG['page_size']
                if current_offset >= RDSTATION_CONFIG['max_offset_limit']:
                    self.logger.warning(f"⚠️ Atingido limite seguro de offset ({current_offset})")
                    self.logger.warning("💡 Para extrair mais dados, considere usar múltiplas execuções com filtros diferentes")
                    break

                self.logger.debug(f"Buscando página {page} (offset: {current_offset})...")

                # Faz chamada com ordenação
                response = self.client.list_contacts(
                    page=page,
                    limit=RDSTATION_CONFIG['page_size'],
                    order=order_field,
                    direction=direction
                )

                if not isinstance(response, dict):
                    self.logger.error(f"Resposta inválida da API: {response}")
                    break

                # Extrai contatos da resposta
                contacts = self._extract_contacts_from_response(response)

                if not contacts:
                    self.logger.info("Nenhum contato encontrado, finalizando...")
                    break

                # Processa contatos
                page_contacts = []
                for contact in contacts:
                    processed_contact = self._process_contact(
                        contact, deals_users_map, deals_data_map
                    )
                    page_contacts.append(processed_contact)

                all_contacts.extend(page_contacts)
                tracker.update(len(page_contacts))

                self.logger.info(f"✅ Página {page}: {len(page_contacts)} contatos processados")

                # Para se atingir test_sample
                if self.test_sample and len(all_contacts) >= self.test_sample:
                    self.logger.info(f"Limite de amostra atingido: {self.test_sample}")
                    break

                # Verifica se há mais páginas
                if len(contacts) < RDSTATION_CONFIG['page_size']:
                    self.logger.info("Última página alcançada (menos contatos que o limite)")
                    break

                page += 1

            except Exception as e:
                error_msg = str(e)
                if "Result window is too large" in error_msg:
                    self.logger.error(f"❌ Limite de 10.000 registros atingido na página {page}")
                    self.logger.error("💡 Extraídos todos os contatos possíveis com a limitação da API")
                    break
                elif "rate limit" in error_msg.lower():
                    self.logger.info("Rate limit atingido, aguardando 60 segundos...")
                    time.sleep(60)
                    continue
                else:
                    self.logger.error(f"Erro na página {page}: {e}")
                    break

        tracker.finish()

        # Log final com estatísticas
        self.logger.info(f"📊 Extração concluída:")
        self.logger.info(f"   - Páginas processadas: {page - 1}")
        self.logger.info(f"   - Contatos extraídos: {len(all_contacts)}")
        self.logger.info(f"   - Offset final: {(page - 1) * RDSTATION_CONFIG['page_size']}")

        if len(all_contacts) >= RDSTATION_CONFIG['max_offset_limit'] - RDSTATION_CONFIG['page_size']:
            self.logger.warning("⚠️ Possível limitação da API - pode haver mais contatos não extraídos")
            self.logger.info("💡 Para extrair todos os dados, considere:")
            self.logger.info("   1. Executar múltiplas vezes com filtros diferentes")
            self.logger.info("   2. Usar ordenação decrescente em execução separada")
            self.logger.info("   3. Filtrar por email ou query específica")

        return all_contacts

    def _extract_most_recent_contacts(self, deals_users_map: dict, deals_data_map: dict) -> List[dict]:
        """Extrai os registros mais recentes usando ordenação decrescente por ID"""
        self.logger.info("📋 Usando ordenação decrescente por ID para obter registros mais recentes")

        all_contacts = []
        page = 1
        max_safe_pages = RDSTATION_CONFIG['max_offset_limit'] // RDSTATION_CONFIG['page_size']

        # Ajusta max_pages se test_sample estiver definido
        if self.test_sample:
            max_safe_pages = min(max_safe_pages, (self.test_sample // RDSTATION_CONFIG['page_size']) + 1)

        tracker = ProgressTracker(0, "Extraindo registros mais recentes do RD Station")

        self.logger.info(f"🎯 Configuração: ordenação por ID (desc), máximo {max_safe_pages} páginas")

        while page <= max_safe_pages:
            try:
                # Rate limiting
                self.rate_limiter.wait_if_needed()

                # Calcula offset atual
                current_offset = (page - 1) * RDSTATION_CONFIG['page_size']
                if current_offset >= RDSTATION_CONFIG['max_offset_limit']:
                    self.logger.warning(f"⚠️ Atingido limite seguro de offset ({current_offset})")
                    self.logger.info("✅ Extração concluída - limite de API respeitado")
                    break

                self.logger.debug(f"Buscando página {page} (offset: {current_offset})...")

                # Faz chamada com ordenação decrescente por ID (mais recentes primeiro)
                response = self.client.list_contacts(
                    page=page,
                    limit=RDSTATION_CONFIG['page_size'],
                    order='id',
                    direction='desc'
                )

                if not isinstance(response, dict):
                    self.logger.error(f"Resposta inválida da API: {response}")
                    break

                # Extrai contatos da resposta
                contacts = self._extract_contacts_from_response(response)

                if not contacts:
                    self.logger.info("Nenhum contato encontrado, finalizando...")
                    break

                # Processa contatos
                page_contacts = []
                for contact in contacts:
                    processed_contact = self._process_contact(
                        contact, deals_users_map, deals_data_map
                    )
                    page_contacts.append(processed_contact)

                all_contacts.extend(page_contacts)
                tracker.update(len(page_contacts))

                self.logger.info(f"✅ Página {page}: {len(page_contacts)} contatos processados (total: {len(all_contacts)})")

                # Para se atingir test_sample
                if self.test_sample and len(all_contacts) >= self.test_sample:
                    self.logger.info(f"Limite de amostra atingido: {self.test_sample}")
                    break

                # Verifica se há mais páginas
                if len(contacts) < RDSTATION_CONFIG['page_size']:
                    self.logger.info("Última página alcançada (menos contatos que o limite)")
                    break

                page += 1

            except Exception as e:
                error_msg = str(e)
                if "Result window is too large" in error_msg:
                    self.logger.error(f"❌ Limite de 10.000 registros atingido na página {page}")
                    self.logger.info("✅ Extraídos todos os contatos possíveis com a limitação da API")
                    break
                elif "rate limit" in error_msg.lower():
                    self.logger.info("Rate limit atingido, aguardando 60 segundos...")
                    time.sleep(60)
                    continue
                else:
                    self.logger.error(f"Erro na página {page}: {e}")
                    break

        tracker.finish()

        self.logger.info(f"📊 Extração de registros mais recentes concluída:")
        self.logger.info(f"   - Páginas processadas: {page - 1}")
        self.logger.info(f"   - Contatos extraídos: {len(all_contacts)}")
        self.logger.info(f"   - Estratégia: ID decrescente (mais recentes primeiro)")

        return all_contacts

    def _load_deals_mapping(self) -> tuple:
        """Carrega mapeamento de deals para vendedores e dados"""
        self.logger.info("Carregando mapeamento de deals...")
        
        deals_users_map = {}
        deals_data_map = {}
        
        try:
            page = 1
            max_pages = 100  # Limite de segurança
            
            while page <= max_pages:
                # Rate limiting
                self.rate_limiter.wait_if_needed()
                
                self.logger.info(f"Carregando deals página {page}...")
                
                response = self.client.list_opportunities(page=page, limit=200)
                
                if not isinstance(response, dict) or 'deals' not in response:
                    break
                
                deals = response['deals']
                if not deals:
                    break
                
                for deal in deals:
                    deal_id = deal.get('id')
                    if deal_id:
                        # Mapeia vendedor
                        user_info = deal.get('user', {})
                        if user_info and isinstance(user_info, dict):
                            user_name = user_info.get('name')
                            if user_name:
                                deals_users_map[deal_id] = user_name
                        
                        # Armazena dados completos do deal
                        deals_data_map[deal_id] = deal
                
                if len(deals) < 200:
                    break
                
                page += 1
            
            self.logger.info(f"✅ Carregado mapeamento de {len(deals_users_map)} deals")
            
        except Exception as e:
            self.logger.warning(f"Erro ao carregar deals: {e}")
        
        return deals_users_map, deals_data_map
    
    def _extract_contacts_from_response(self, response: dict) -> List[dict]:
        """Extrai contatos da resposta da API"""
        contacts = None
        
        if 'contacts' in response:
            contacts = response['contacts']
        elif 'data' in response:
            if isinstance(response['data'], dict) and 'contacts' in response['data']:
                contacts = response['data']['contacts']
            elif isinstance(response['data'], list):
                contacts = response['data']
        
        return contacts or []
    
    def _process_contact(self, contact: dict, deals_users_map: dict, deals_data_map: dict) -> dict:
        """Processa um contato individual"""
        
        # Informações básicas
        contact_id = contact.get('id', '')
        created_at = self._safe_get_date(contact.get('created_at', ''))
        
        # Email
        email = ""
        emails = contact.get('emails', [])
        if emails and isinstance(emails[0], dict):
            email = emails[0].get('email', '')
        
        # Telefone
        phone = ""
        phones = contact.get('phones', [])
        if phones and isinstance(phones[0], dict):
            phone = phones[0].get('phone', '')
        
        # Nome
        full_name = contact.get('name', '')
        first_name, processed_full_name = self._safe_get_name_parts(full_name)
        
        # Campos customizados
        custom_fields = contact.get('contact_custom_fields', [])
        
        # Extrai campos customizados
        extracted_fields = {}
        for field in custom_fields:
            if isinstance(field, dict):
                field_id = field.get('custom_field_id')
                if field_id in self.contact_fields:
                    field_name = self.contact_fields[field_id]
                    extracted_fields[field_name] = str(field.get('value', '') or '')
        
        # Extrai dados de deals
        deal_fields = self._extract_deal_fields(contact, deals_data_map)
        
        # Obtém vendedor
        vendedor = self._get_responsible_user_name(contact, deals_users_map)
        
        # Monta registro
        return {
            'contact_id': contact_id,  # Adiciona ID para deduplicação
            'cnpj_cpf': extracted_fields.get('CPF', ''),
            'email': email,
            'Dt_cadastro': created_at,
            'celular': phone,
            'Locale': 'br',
            'PrimeiroNome': first_name,
            'nome_completo': processed_full_name,
            'Cidade': extracted_fields.get('Cidade', ''),
            'Estado': extracted_fields.get('Estado', ''),
            'TipoEmpresa': 'Bamaq Consórcio',
            'TipoBem': 'Carta de Crédito',
            'TipoSimulacao': 'Consórcio',
            'ValorSimulacao': extracted_fields.get('ValorSimulacao', ''),
            'Campaign': deal_fields.get('Campaign', ''),
            'Source': deal_fields.get('Source', ''),
            'Medium': deal_fields.get('Medium', ''),
            'Term': deal_fields.get('Term', ''),
            'Content': deal_fields.get('Content', ''),
            'PontoVenda': deal_fields.get('PontoVenda', ''),
            'PlanoVenda': deal_fields.get('PlanoVenda', ''),
            'dt_nascimento': self._format_birth_date(extracted_fields.get('Data de Nascimento', '')),
            'Optin_consorcio_email': 'True',
            'Optin_consorcio_SMS': 'True',
            'Optin_consorcio_whatsapp': 'True',
            'Optin_seguros_email': '',
            'Optin_seguros_SMS': '',
            'Optin_seguros_wpp': '',
            'Optin_digital_email': '',
            'Optin_digital_SMS': '',
            'Optin_digital_whatsapp': '',
            'Optin_capital_email': '',
            'Optin_capital_SMS': '',
            'Optin_capital_whatsapp': '',
            'Dt_simulacao': created_at,
            'Nome vendedor': vendedor,
        }
    
    def _safe_get_date(self, date_str: str) -> str:
        """Extrai data de string ISO"""
        if not date_str:
            return ""
        try:
            return date_str.split('T')[0]
        except:
            return str(date_str)
    
    def _safe_get_name_parts(self, full_name: str) -> tuple:
        """Extrai primeiro nome e nome completo"""
        if not full_name:
            return "", ""
        
        # Remove vírgula inicial
        full_name = full_name.strip().lstrip(',').strip()
        
        if not full_name:
            return "", ""
        
        parts = full_name.split()
        if parts:
            first_name = parts[0]
            return first_name, full_name
        
        return "", full_name
    
    def _extract_deal_fields(self, contact: dict, deals_data_map: dict) -> dict:
        """Extrai campos de marketing/vendas dos deals"""
        deal_fields = {
            'Campaign': '', 'Source': '', 'Medium': '', 'Term': '', 'Content': '',
            'PontoVenda': '', 'PlanoVenda': '', 'ValorSimulacaoDeal': ''
        }
        
        deals = contact.get('deals', [])
        if deals and deals_data_map:
            for deal in deals:
                deal_id = deal.get('id') or deal.get('_id')
                if deal_id and deal_id in deals_data_map:
                    deal_data = deals_data_map[deal_id]
                    deal_custom_fields = deal_data.get('deal_custom_fields', [])
                    
                    # Extrai campos customizados do deal
                    for field in deal_custom_fields:
                        if isinstance(field, dict):
                            field_id = field.get('custom_field_id')
                            if field_id in self.deal_fields:
                                field_name = self.deal_fields[field_id]
                                value = str(field.get('value', '') or '')
                                
                                # Mapeia para campos finais
                                if field_name == 'ID_Campanha' and value and not deal_fields['Campaign']:
                                    deal_fields['Campaign'] = value
                                elif field_name == 'UTM_Source' and value and not deal_fields['Source']:
                                    deal_fields['Source'] = value
                                elif field_name == 'UTM_Medium' and value and not deal_fields['Medium']:
                                    deal_fields['Medium'] = value
                                elif field_name == 'UTM_Term' and value and not deal_fields['Term']:
                                    deal_fields['Term'] = value
                                elif field_name == 'UTM_Content' and value and not deal_fields['Content']:
                                    deal_fields['Content'] = value
                                elif field_name == 'Ponto_de_Venda' and value and not deal_fields['PontoVenda']:
                                    deal_fields['PontoVenda'] = value
                                elif field_name == 'Plano_de_venda' and value and not deal_fields['PlanoVenda']:
                                    deal_fields['PlanoVenda'] = value
        
        return deal_fields
    
    def _get_responsible_user_name(self, contact: dict, deals_users_map: dict) -> str:
        """Extrai nome do vendedor responsável"""
        if not deals_users_map:
            return ""
        
        deals = contact.get('deals', [])
        if not deals:
            return ""
        
        for deal in deals:
            if isinstance(deal, dict):
                deal_id = deal.get('id') or deal.get('_id')
                if deal_id and deal_id in deals_users_map:
                    return deals_users_map[deal_id]
            elif isinstance(deal, str):
                if deal in deals_users_map:
                    return deals_users_map[deal]
        
        return ""
    
    def _format_birth_date(self, date_str: str) -> str:
        """Converte DD/MM/YYYY para YYYY-MM-DD"""
        if not date_str or '/' not in date_str:
            return date_str
        
        try:
            parts = date_str.split('/')
            if len(parts) == 3:
                day, month, year = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        except:
            pass
        
        return date_str
    
    def _validate_rdstation_data(self, df: pd.DataFrame):
        """Valida dados do RD Station"""
        if df.empty:
            self.logger.warning("Nenhum dado extraído do RD Station")
            return
        
        # Estatísticas de qualidade
        total_records = len(df)
        cpf_count = df['cnpj_cpf'].notna().sum()
        email_count = df['email'].notna().sum()
        phone_count = df['celular'].notna().sum()
        
        self.logger.info(f"Qualidade dos dados RD Station:")
        self.logger.info(f"- Total de registros: {total_records}")
        self.logger.info(f"- CPF preenchido: {cpf_count} ({cpf_count/total_records*100:.1f}%)")
        self.logger.info(f"- Email preenchido: {email_count} ({email_count/total_records*100:.1f}%)")
        self.logger.info(f"- Telefone preenchido: {phone_count} ({phone_count/total_records*100:.1f}%)")

    def _validate_extraction_completeness(self, extracted_count: int):
        """Valida se a extração foi completa baseada no número de registros extraídos"""
        max_api_limit = 10000
        safe_limit = RDSTATION_CONFIG['max_offset_limit']
        
        if extracted_count >= safe_limit:
            self.logger.warning(f"⚠️ ALERTA: Extraídos {extracted_count} registros, próximo ao limite da API")
            self.logger.warning(f"📊 Limite seguro configurado: {safe_limit}")
            self.logger.warning(f"📊 Limite máximo da API: {max_api_limit}")
            
            # Calcula estimativa de dados perdidos
            estimated_missing = max_api_limit - extracted_count
            if estimated_missing > 0:
                self.logger.warning(f"📊 Possíveis registros não extraídos: ~{estimated_missing}")
                
            self.logger.info("🔄 Estratégia de extração múltipla implementada para contornar limitações")
            self.logger.info("✅ Recomendação: Executar múltiplas extrações com filtros diferentes se necessário")
            
        else:
            self.logger.info(f"✅ Extração completa: {extracted_count} registros (abaixo do limite de {safe_limit})")
            
        # Adiciona checkpoint para monitoramento
        if extracted_count > 0:
            coverage_percentage = min(100, (extracted_count / max_api_limit) * 100)
            self.logger.info(f"📊 Cobertura estimada: {coverage_percentage:.1f}% dos dados disponíveis")

# =============================================================================
# RATE LIMITER
# =============================================================================

class RateLimiter:
    """Rate limiter para controlar frequência de requests"""
    
    def __init__(self, requests_per_minute: int, requests_per_second: int = None):
        self.requests_per_minute = requests_per_minute
        self.requests_per_second = requests_per_second or (requests_per_minute // 60)
        self.last_request_time = 0
        self.request_count = 0
        self.minute_start = time.time()
        self.lock = Lock()
    
    def wait_if_needed(self):
        """Aguarda se necessário para respeitar rate limit"""
        with self.lock:
            current_time = time.time()
            
            # Reset contador por minuto
            if current_time - self.minute_start >= 60:
                self.request_count = 0
                self.minute_start = current_time
            
            # Verifica limite por segundo
            if self.requests_per_second:
                time_since_last = current_time - self.last_request_time
                min_interval = 1.0 / self.requests_per_second
                
                if time_since_last < min_interval:
                    wait_time = min_interval - time_since_last
                    time.sleep(wait_time)
                    current_time = time.time()
            
            # Verifica limite por minuto
            if self.request_count >= self.requests_per_minute:
                wait_time = 60 - (current_time - self.minute_start)
                if wait_time > 0:
                    time.sleep(wait_time)
                    self.request_count = 0
                    self.minute_start = time.time()
            
            self.last_request_time = current_time
            self.request_count += 1

# =============================================================================
# EXTRAÇÃO ORBBITS (MYSQL)
# =============================================================================

class OrbbitsExtractor:
    """Extrator especializado para Orbbits"""
    
    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.db_name = 'orbbits'
        self.test_sample = test_sample
        self.sql_loader = SQLLoader()
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_origin(self) -> pd.DataFrame:
        """Extrai dados de origem do Orbbits"""
        self.logger.info("Extraindo dados de origem do Orbbits...")
        query = """
        SELECT
            contractnumber,
            origin,
            lgpd
        FROM orbbits_partners.origin_search
        WHERE contractnumber IS NOT NULL
        """
        if self.test_sample:
            query += f"\nORDER BY contractnumber\nLIMIT {self.test_sample}"
        try:
            self.logger.info("[Orbbits][origin] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][origin] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][origin] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)
                self.logger.info(f"[Orbbits][origin] pd.read_sql executado. Registros extraídos: {len(df)}")
                df['contractnumber'] = df['contractnumber'].astype(str)
                self.logger.info(f"✅ {len(df)} registros de origem extraídos do Orbbits")
                return df
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair origem do Orbbits: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_payments(self) -> pd.DataFrame:
        """Extrai dados de pagamentos do Orbbits"""
        self.logger.info("Extraindo dados de pagamentos do Orbbits...")
        query = """
             SELECT distinct
            p.newcon_proposal_contract_number,
                (select payment_link from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and type = 'bill' order by id desc limit 1 ) as payment_link_bill,
                (select payment_link from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and type = 'credit_card' order by id desc limit 1 ) as payment_link_credit_card,
                (select payment_link from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and type = 'pix' order by id desc  limit 1) as payment_link_pix,
                (select payment_date from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and status = 'paid' order by id desc  limit 1) as payment_date,
                (select type from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and status = 'paid' and payment_date  is not null order by id desc  limit 1) as type
            FROM orbbits_charges.payments p
        """
        if self.test_sample:
            query += f"\nORDER BY newcon_proposal_contract_number\nLIMIT {self.test_sample}"
        try:
            self.logger.info("[Orbbits][payments] Abrindo conexão com o banco...")
            try:
                with get_database_connection(self.db_name) as conn:
                    self.logger.info("[Orbbits][payments] Conexão aberta com sucesso.")
                    self.logger.info("[Orbbits][payments] Executando pd.read_sql...")                    
                    df = pd.read_sql(query, conn)
                    self.logger.info(f"[Orbbits][payments] pd.read_sql executado. Registros extraídos: {len(df)}")
                    df['newcon_proposal_contract_number'] = df['newcon_proposal_contract_number'].astype(str)
                    self.logger.info(f"✅ {len(df)} registros de pagamentos extraídos do Orbbits")
                    return df
            except Exception as conn_exc:
                self.logger.error(f"[Orbbits][payments] ERRO ao abrir conexão: {conn_exc}")
                raise
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair pagamentos do Orbbits: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_sales(self) -> pd.DataFrame:
        """Extrai dados de vendas do Orbbits"""
        self.logger.info("Extraindo dados de vendas do Orbbits...")
        query = """
            SELECT 
            p.newcon_id_proposal as proposal_id, 
            s.sale_coupon as sale_coupon
            from
            orbbits_leads.proposals p
            join orbbits_partners.sales s on
            s.proposal_id = p.id
            where  coalesce(s.sale_coupon,'') <> ''
            order by
            p.newcon_id_proposal  
        """
        if self.test_sample:
            query += f"\nORDER BY proposal_id\nLIMIT {self.test_sample}"
        try:
            self.logger.info("[Orbbits][sales] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][sales] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][sales] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)
                self.logger.info(f"[Orbbits][sales] pd.read_sql executado. Registros extraídos: {len(df)}")
                df['proposal_id'] = df['proposal_id'].astype(str)
                self.logger.info(f"✅ {len(df)} registros de vendas extraídos do Orbbits")
                return df
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair vendas do Orbbits: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_proposals(self) -> pd.DataFrame:
        """Extrai dados de propostas do Orbbits usando SQLLoader"""
        self.logger.info("Extraindo dados de propostas do Orbbits...")

        # Carregar consulta usando SQLLoader
        query = self.sql_loader.get_query('orbbits', 'proposals')

        # Adicionar LIMIT se test_sample estiver definido
        if self.test_sample:
            query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

        try:
            self.logger.info("[Orbbits][proposals] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][proposals] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][proposals] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)
                self.logger.info(f"✅ {len(df)} propostas extraídas do Orbbits")
                return df
        except Exception as e:
            self.logger.error(f"Erro ao extrair propostas do Orbbits: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_prices(self) -> pd.DataFrame:
        """Extrai dados de preços do Orbbits"""
        self.logger.info("Extraindo dados de preços do Orbbits...")
        query = """
        WITH primeira_parc AS (
            SELECT
                id_plano_de_venda,
                id_ponto_de_venda,
                id_bem,
                GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_sem_seguro) SEPARATOR '; ') AS valor_primeira_parcela,
                GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_com_seguro) SEPARATOR '; ') AS valor_primeira_parcela_c_seguro
            FROM orbbitsdb.lista_preco_progressao
            WHERE parc_inic = 1
            GROUP BY id_plano_de_venda, id_ponto_de_venda, id_bem
        ),
        demais_parc AS (
            SELECT
                id_plano_de_venda,
                id_ponto_de_venda,
                id_bem,
                GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_sem_seguro) SEPARATOR '; ') AS valor_parcelas,
                GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_com_seguro) SEPARATOR '; ') AS valor_parcelas_c_seguro
            FROM orbbitsdb.lista_preco_progressao
            WHERE parc_inic > 1
            GROUP BY id_plano_de_venda, id_ponto_de_venda, id_bem
        )
        SELECT
            primeira_parc.id_plano_de_venda,
            primeira_parc.id_ponto_de_venda,
            primeira_parc.id_bem,
            primeira_parc.valor_primeira_parcela,
            primeira_parc.valor_primeira_parcela_c_seguro,
            demais_parc.valor_parcelas,
            demais_parc.valor_parcelas_c_seguro
        FROM primeira_parc
        INNER JOIN demais_parc ON 
            demais_parc.id_plano_de_venda = primeira_parc.id_plano_de_venda
            AND demais_parc.id_ponto_de_venda = primeira_parc.id_ponto_de_venda
            AND demais_parc.id_bem = primeira_parc.id_bem
        """
        if self.test_sample:
            query += f"\nORDER BY primeira_parc.id_plano_de_venda\nLIMIT {self.test_sample}"
        try:
            self.logger.info("[Orbbits][prices] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][prices] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][prices] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)
                self.logger.info(f"[Orbbits][prices] pd.read_sql executado. Registros extraídos: {len(df)}")
                self.logger.info(f"✅ {len(df)} registros de preços extraídos do Orbbits")
                return df
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair preços do Orbbits: {e}")
            raise

# =============================================================================
# QUIVER EXTRACTOR
# =============================================================================

class QuiverExtractor:
    """Extrator especializado para Quiver"""

    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.db_name = 'quiver'
        self.test_sample = test_sample

        # Usar SQLLoader para carregar consultas
        from salesforce_integration.sql_loader import sql_loader
        self.sql_loader = sql_loader

    def _load_sql_query(self, table_name: str) -> str:
        """Carrega consulta SQL usando SQLLoader"""
        try:
            # Carregar consulta usando SQLLoader
            query = self.sql_loader.get_query('quiver', table_name)

            # Adicionar LIMIT se test_sample estiver definido
            if self.test_sample:
                query = self.sql_loader.add_test_sample_limit(query, self.test_sample)

            return query
        except Exception as e:
            self.logger.error(f"Erro ao carregar consulta quiver_{table_name}: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_clients(self) -> pd.DataFrame:
        """Extrai dados de clientes do Quiver"""
        self.logger.info("Extraindo dados de clientes do Quiver...")

        query = self._load_sql_query('clientes')

        try:
            self.logger.info("[Quiver][clients] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Quiver][clients] Conexão aberta com sucesso.")
                self.logger.info("[Quiver][clients] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Quiver][clients] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de clientes do Quiver: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_leads(self) -> pd.DataFrame:
        """Extrai dados de leads do Quiver"""
        self.logger.info("Extraindo dados de leads do Quiver...")

        query = self._load_sql_query('leads')

        try:
            self.logger.info("[Quiver][leads] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Quiver][leads] Conexão aberta com sucesso.")
                self.logger.info("[Quiver][leads] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Quiver][leads] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de leads do Quiver: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_products(self) -> pd.DataFrame:
        """Extrai dados de produtos do Quiver"""
        self.logger.info("Extraindo dados de produtos do Quiver...")

        query = self._load_sql_query('produtos')

        try:
            self.logger.info("[Quiver][products] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Quiver][products] Conexão aberta com sucesso.")
                self.logger.info("[Quiver][products] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Quiver][products] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de produtos do Quiver: {e}")
            raise

    @retry_decorator(max_attempts=3, delay=2)
    def extract_proposals(self) -> pd.DataFrame:
        """Extrai dados de propostas do Quiver"""
        self.logger.info("Extraindo dados de propostas do Quiver...")

        query = self._load_sql_query('propostas')

        try:
            self.logger.info("[Quiver][proposals] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Quiver][proposals] Conexão aberta com sucesso.")
                self.logger.info("[Quiver][proposals] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)

                self.logger.info(f"[Quiver][proposals] Dados extraídos: {len(df)} registros")
                return df

        except Exception as e:
            self.logger.error(f"Erro na extração de propostas do Quiver: {e}")
            raise

# =============================================================================
# FUNÇÕES PRINCIPAIS DE EXTRAÇÃO
# =============================================================================

def extract_newcon_clients(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair clientes do NewCon"""
    extractor = NewConExtractor(test_sample)
    return extractor.extract_clients()

def extract_newcon_leads(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair leads do NewCon"""
    extractor = NewConExtractor(test_sample)
    return extractor.extract_leads()

def extract_newcon_products(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair produtos do NewCon"""
    extractor = NewConExtractor(test_sample)
    return extractor.extract_products()

def extract_newcon_proposals(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair propostas do NewCon"""
    extractor = NewConExtractor(test_sample)
    return extractor.extract_proposals()

def extract_orbbits_origin(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair origem do Orbbits"""
    extractor = OrbbitsExtractor(test_sample)
    return extractor.extract_origin()

def extract_orbbits_payments(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair pagamentos do Orbbits"""
    extractor = OrbbitsExtractor(test_sample)
    return extractor.extract_payments()

def extract_orbbits_sales(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair vendas do Orbbits"""
    extractor = OrbbitsExtractor(test_sample)
    return extractor.extract_sales()

def extract_orbbits_prices(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair preços do Orbbits"""
    extractor = OrbbitsExtractor(test_sample)
    return extractor.extract_prices()

def extract_orbbits_proposals(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair propostas do Orbbits"""
    extractor = OrbbitsExtractor(test_sample)
    return extractor.extract_proposals()

def extract_rdstation_leads(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair leads do RD Station"""
    extractor = RDStationExtractor(test_sample)
    return extractor.extract_leads()

def extract_quiver_clients(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair clientes do Quiver"""
    extractor = QuiverExtractor(test_sample)
    return extractor.extract_clients()

def extract_quiver_leads(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair leads do Quiver"""
    extractor = QuiverExtractor(test_sample)
    return extractor.extract_leads()

def extract_quiver_products(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair produtos do Quiver"""
    extractor = QuiverExtractor(test_sample)
    return extractor.extract_products()

def extract_quiver_proposals(test_sample: int = None) -> pd.DataFrame:
    """Função principal para extrair propostas do Quiver"""
    extractor = QuiverExtractor(test_sample)
    return extractor.extract_proposals()

if __name__ == "__main__":
    # Teste básico
    print("=== TESTE DOS EXTRATORES ===")

    # Teste NewCon
    try:
        df = extract_newcon_clients(test_sample=5)
        print(f"✅ NewCon Clientes: {len(df)} registros")
    except Exception as e:
        print(f"❌ NewCon Clientes: {e}")

    # Teste Quiver
    try:
        df = extract_quiver_clients(test_sample=5)
        print(f"✅ Quiver Clientes: {len(df)} registros")
    except Exception as e:
        print(f"❌ Quiver Clientes: {e}")

    # Teste Orbbits
    try:
        df = extract_orbbits_origin(test_sample=5)
        print(f"✅ Orbbits Origin: {len(df)} registros")
    except Exception as e:
        print(f"❌ Orbbits Origin: {e}")

    print("=== FIM DOS TESTES ===")
