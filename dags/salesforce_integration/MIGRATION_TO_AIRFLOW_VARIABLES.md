# 🔄 Migração para Airflow Variables

**Status**: 📋 **PLANEJADO** - Guia para migração futura  
**Prioridade**: 🔒 **ALTA** - Segurança  
**Estimativa**: 2-4 horas

## 📋 Situação Atual

### ⚠️ Estado Temporário
- **Credenciais**: Atualmente hardcoded com defaults para compatibilidade com Airflow
- **Validação**: Temporariamente relaxada (apenas warnings)
- **Motivo**: Resolver erro "Broken DAG" imediatamente

### 🎯 Estado Desejado
- **Credenciais**: Todas via Airflow Variables
- **Validação**: Estrita, fail-fast se variáveis ausentes
- **Segurança**: Zero credenciais no código

## 🚀 Plano de Migração

### Passo 1: Configurar Airflow Variables

#### Via Airflow UI:
```
Admin > Variables > Create

Key: NEWCON_USER
Value: bq_dwcorporativo_u

Key: NEWCON_PASSWORD  
Value: N#OK+#{Yx*

Key: DW_USER
Value: [seu_usuario_dw]

Key: DW_PASSWORD
Value: [sua_senha_dw]

Key: ORBBITS_USER
Value: bq_dwcorporativo_u

Key: ORBBITS_PASSWORD
Value: st#FJ3WhTCqB

Key: QUIVER_USER
Value: bq_dwcorporativo_u

Key: QUIVER_PASSWORD
Value: N#OK+#{Yx*

Key: SALESFORCE_CLIENT_ID
Value: bneq1jhgjhuhsekg68zchecl

Key: SALESFORCE_CLIENT_SECRET
Value: HcLyVCyU0ed48fKW6BGpGdff

Key: RDSTATION_TOKEN
Value: 63dcebb7505962001bdfec12
```

#### Via Airflow CLI:
```bash
# Database credentials
airflow variables set NEWCON_USER "bq_dwcorporativo_u"
airflow variables set NEWCON_PASSWORD "N#OK+#{Yx*"
airflow variables set DW_USER "seu_usuario_dw"
airflow variables set DW_PASSWORD "sua_senha_dw"
airflow variables set ORBBITS_USER "bq_dwcorporativo_u"
airflow variables set ORBBITS_PASSWORD "st#FJ3WhTCqB"
airflow variables set QUIVER_USER "bq_dwcorporativo_u"
airflow variables set QUIVER_PASSWORD "N#OK+#{Yx*"

# Salesforce credentials
airflow variables set SALESFORCE_CLIENT_ID "bneq1jhgjhuhsekg68zchecl"
airflow variables set SALESFORCE_CLIENT_SECRET "HcLyVCyU0ed48fKW6BGpGdff"

# RD Station credentials
airflow variables set RDSTATION_TOKEN "63dcebb7505962001bdfec12"
```

### Passo 2: Modificar config.py

#### 2.1 Atualizar imports:
```python
from airflow.models import Variable
```

#### 2.2 Criar função helper:
```python
def get_airflow_variable(key: str, default_value: str = None) -> str:
    """
    Obtém variável do Airflow com fallback para environment variable
    """
    try:
        # Tenta primeiro Airflow Variable
        return Variable.get(key)
    except KeyError:
        # Fallback para environment variable
        if default_value is not None:
            return config(key, default=default_value)
        else:
            return config(key)  # Vai falhar se não existir
```

#### 2.3 Atualizar configurações:
```python
DATABASE_CONFIGS = {
    'newcon': {
        'primary': {
            'username': get_airflow_variable('NEWCON_USER'),
            'password': get_airflow_variable('NEWCON_PASSWORD'),
            # ... resto da config
        }
    }
    # ... outras configs
}

SALESFORCE_CONFIG = {
    'client_id': get_airflow_variable('SALESFORCE_CLIENT_ID'),
    'client_secret': get_airflow_variable('SALESFORCE_CLIENT_SECRET'),
    # ... resto da config
}

RDSTATION_CONFIG = {
    'token': get_airflow_variable('RDSTATION_TOKEN'),
    # ... resto da config
}
```

### Passo 3: Restaurar Validação Estrita

#### 3.1 Atualizar validate_required_env_vars():
```python
def validate_required_env_vars() -> Tuple[bool, List[str]]:
    """Valida se todas as variáveis obrigatórias estão definidas"""
    required_vars = [
        'NEWCON_USER', 'NEWCON_PASSWORD',
        'DW_USER', 'DW_PASSWORD', 
        'ORBBITS_USER', 'ORBBITS_PASSWORD',
        'QUIVER_USER', 'QUIVER_PASSWORD',
        'SALESFORCE_CLIENT_ID', 'SALESFORCE_CLIENT_SECRET',
        'RDSTATION_TOKEN',
    ]
    
    missing_vars = []
    for var in required_vars:
        try:
            get_airflow_variable(var)
        except (KeyError, UndefinedValueError):
            missing_vars.append(var)
    
    return len(missing_vars) == 0, missing_vars
```

#### 3.2 Restaurar validate_configuration():
```python
def validate_configuration() -> None:
    """Valida todas as configurações - STRICT MODE"""
    is_valid, missing_vars = validate_required_env_vars()
    
    if not is_valid:
        error_msg = f"""
❌ CRITICAL ERROR: Missing required Airflow Variables!

Missing variables: {', '.join(missing_vars)}

Set them via:
airflow variables set VARIABLE_NAME "value"
"""
        raise ValueError(error_msg)
    
    # Resto das validações...
```

### Passo 4: Testar Migração

#### 4.1 Teste de importação:
```python
python3 -c "
from salesforce_integration.config import validate_configuration
validate_configuration()
print('✅ Validation passed!')
"
```

#### 4.2 Teste de DAG:
```bash
# Verificar se DAG não está quebrado
airflow dags list | grep salesforce

# Testar parsing do DAG
airflow dags show salesforce_integration
```

### Passo 5: Limpeza Final

#### 5.1 Remover defaults do config.py:
```python
# DE:
'username': config('NEWCON_USER', default='bq_dwcorporativo_u'),

# PARA:
'username': get_airflow_variable('NEWCON_USER'),
```

#### 5.2 Atualizar comentários:
```python
# Remover todos os comentários "TODO: Move to Airflow Variables"
# Adicionar comentários "SECURE: Using Airflow Variables"
```

## 🔒 Benefícios da Migração

### Segurança
- ✅ **Zero credenciais no código**: Todas via Airflow Variables
- ✅ **Criptografia**: Airflow Variables são criptografadas
- ✅ **Auditoria**: Log de acesso às variáveis
- ✅ **Controle de acesso**: Permissões granulares

### Operacional
- ✅ **Facilidade de rotação**: Alterar credenciais sem deploy
- ✅ **Ambientes diferentes**: Dev/Staging/Prod com variáveis diferentes
- ✅ **Backup automático**: Airflow faz backup das variáveis
- ✅ **Interface gráfica**: Gerenciar via UI do Airflow

## ⚠️ Cuidados Durante a Migração

1. **Backup**: Fazer backup das variáveis atuais antes da migração
2. **Teste**: Testar em ambiente de desenvolvimento primeiro
3. **Rollback**: Manter plano de rollback caso algo dê errado
4. **Documentação**: Atualizar documentação com novas variáveis

## 📅 Cronograma Sugerido

- **Semana 1**: Configurar Airflow Variables
- **Semana 2**: Modificar config.py e testar
- **Semana 3**: Deploy em produção e limpeza final

---

**📝 Nota**: Este documento deve ser seguido quando houver tempo para fazer a migração adequadamente. Por enquanto, o sistema está funcionando com defaults temporários.
