#!/usr/bin/env python3
"""
Pipeline Executor - Extracted from etl_main.py

Command-line interface and main execution logic for the ETL pipeline.
This module was extracted from the monolithic etl_main.py file as part of the refactoring effort.
"""

import sys
import argparse
import logging
from typing import Optional, List

from salesforce_integration.orchestrators.etl_pipeline import ETLPipeline
from salesforce_integration.reports.execution_reporter import generate_execution_report, create_dag_template
from salesforce_integration.database_connections import test_all_connections
from salesforce_integration.salesforce_client import test_salesforce_connection


def parse_arguments():
    """Parseia argumentos da linha de comando"""
    parser = argparse.ArgumentParser(
        description="ETL Pipeline para Salesforce Marketing Cloud",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:
  python pipeline_executor.py                           # Executa pipeline completo
  python pipeline_executor.py --dry-run                 # Executa em modo dry-run
  python pipeline_executor.py --table tb_clientes       # Executa apenas clientes
  python pipeline_executor.py --tables tb_produtos,tb_clientes  # Executa produtos e clientes
  python pipeline_executor.py --generate-dag            # Gera template de DAG
        """
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Executa em modo dry-run (não envia dados para Salesforce)'
    )
    
    parser.add_argument(
        '--table',
        type=str,
        help='Executa pipeline para uma única tabela'
    )
    
    parser.add_argument(
        '--tables',
        type=str,
        help='Executa pipeline para tabelas específicas (separadas por vírgula)'
    )
    
    parser.add_argument(
        '--sequence',
        type=str,
        help='Sequência customizada de tabelas (separadas por vírgula)'
    )
    
    parser.add_argument(
        '--generate-dag',
        action='store_true',
        help='Gera template de DAG para Airflow'
    )
    
    parser.add_argument(
        '--test-connections',
        action='store_true',
        help='Testa apenas as conexões'
    )

    parser.add_argument(
        '--test-sample',
        type=int,
        default=None,
        help='Executa com amostra limitada de registros (ex: --test-sample 100)'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Nível de log'
    )
    
    return parser.parse_args()


def main():
    """Função principal"""
    args = parse_arguments()
    
    # Configura nível de log
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    logger = logging.getLogger(__name__)
    
    # Gera template DAG
    if args.generate_dag:
        template = create_dag_template()
        print(template)
        return 0
    
    # Testa apenas conexões
    if args.test_connections:
        print("Testando conexões...")
        if test_all_connections() and test_salesforce_connection():
            print("✅ Todas as conexões OK")
            return 0
        else:
            print("❌ Falha nas conexões")
            return 1
    
    # Configura filtros de tabela
    table_filter = None
    if args.table:
        table_filter = [args.table]
    elif args.tables:
        table_filter = [t.strip() for t in args.tables.split(',')]
    
    # Configura sequência
    table_sequence = None
    if args.sequence:
        table_sequence = [t.strip() for t in args.sequence.split(',')]
    
    # Executa pipeline
    pipeline = ETLPipeline(dry_run=args.dry_run, test_sample=args.test_sample)
    
    try:
        result = pipeline.run_pipeline(
            table_filter=table_filter,
            table_sequence=table_sequence
        )
        
        # Gera relatório
        report = generate_execution_report(result)
        print(report)
        
        return 0 if result['success'] else 1
        
    except KeyboardInterrupt:
        logger.info("Pipeline interrompido pelo usuário")
        return 1
    except Exception as e:
        logger.error(f"Erro inesperado: {e}")
        return 1
    finally:
        pipeline.cleanup()


if __name__ == "__main__":
    sys.exit(main())
