#!/usr/bin/env python3
"""
Airflow Loading Tasks - Extracted from etl_main.py

All Airflow task functions for data loading, organized by type:
- General loading tasks
- Table-specific parallel loading tasks

This module was extracted from the monolithic etl_main.py file as part of the refactoring effort.
"""

import logging
from typing import Dict, Any, Optional

from salesforce_integration.salesforce_client import process_etl_pipeline


# =============================================================================
# GENERAL LOADING TASKS
# =============================================================================

def airflow_load_task(table_name: str = None, **context) -> Dict[str, Any]:
    """Task de carregamento para Airflow"""
    try:
        # Recupera dados da transformação
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='transformation_result')
        else:
            raise ValueError("Dados de transformação não encontrados")
        
        # Carrega dados
        tables_data = transformed_data['transformed_data']
        
        if table_name:
            # Carrega apenas tabela específica
            if table_name in tables_data:
                single_table_data = {table_name: tables_data[table_name]}
                result = process_etl_pipeline(single_table_data, dry_run=False)
            else:
                raise ValueError(f"Tabela '{table_name}' não encontrada")
        else:
            # Carrega todas as tabelas
            result = process_etl_pipeline(tables_data, dry_run=False)
        
        return result
    except Exception as e:
        logging.error(f"Erro na task de carregamento: {e}")
        raise


# =============================================================================
# TABLE-SPECIFIC PARALLEL LOADING TASKS
# =============================================================================

def airflow_load_produtos_parallel_task(**context) -> Dict[str, Any]:
    """Task de carregamento paralelo para produtos"""
    try:
        # Recupera dados da transformação de produtos
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='produtos_transformation_result')
        else:
            raise ValueError("Dados de transformação de produtos não encontrados")
        
        # Carrega dados de produtos
        if transformed_data['success'] and not transformed_data['transformed_data']['tb_produtos'].empty:
            result = process_etl_pipeline(transformed_data['transformed_data'], dry_run=False)
            logging.info(f"✅ Carregamento Produtos: {result.get('total_records', 0)} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_produtos'],
                'message': 'Nenhum dado de produtos para carregar'
            }
            logging.info("⚠️ Carregamento Produtos: Nenhum dado para carregar")
        
        return result
    except Exception as e:
        logging.error(f"Erro no carregamento de produtos: {e}")
        raise


def airflow_load_clientes_parallel_task(**context) -> Dict[str, Any]:
    """Task de carregamento paralelo para clientes"""
    try:
        # Recupera dados da transformação de clientes
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='clientes_transformation_result')
        else:
            raise ValueError("Dados de transformação de clientes não encontrados")

        # Verifica se os dados foram encontrados
        if transformed_data is None:
            logging.error("❌ Dados de transformação de clientes não encontrados no XCom")
            raise ValueError("Dados de transformação de clientes não encontrados no XCom")

        # Carrega dados de clientes
        if (transformed_data.get('success', False) and 
            'transformed_data' in transformed_data and 
            'tb_clientes' in transformed_data['transformed_data'] and 
            not transformed_data['transformed_data']['tb_clientes'].empty):
            
            result = process_etl_pipeline(transformed_data['transformed_data'], dry_run=False)
            logging.info(f"✅ Carregamento Clientes: {result.get('total_records', 0)} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_clientes'],
                'message': 'Nenhum dado de clientes para carregar'
            }
            logging.info("⚠️ Carregamento Clientes: Nenhum dado para carregar")
        
        return result
    except Exception as e:
        logging.error(f"Erro no carregamento de clientes: {e}")
        raise


def airflow_load_leads_parallel_task(**context) -> Dict[str, Any]:
    """Task de carregamento paralelo para leads"""
    try:
        # Recupera dados da transformação de leads
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='leads_transformation_result')
        else:
            raise ValueError("Dados de transformação de leads não encontrados")

        # Verifica se os dados foram encontrados
        if transformed_data is None:
            logging.error("❌ Dados de transformação de leads não encontrados no XCom")
            raise ValueError("Dados de transformação de leads não encontrados no XCom")

        # Carrega dados de leads
        if (transformed_data.get('success', False) and 
            'transformed_data' in transformed_data and 
            'tb_leads' in transformed_data['transformed_data'] and 
            not transformed_data['transformed_data']['tb_leads'].empty):
            
            result = process_etl_pipeline(transformed_data['transformed_data'], dry_run=False)
            logging.info(f"✅ Carregamento Leads: {result.get('total_records', 0)} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_leads'],
                'message': 'Nenhum dado de leads para carregar'
            }
            logging.info("⚠️ Carregamento Leads: Nenhum dado para carregar")
        
        return result
    except Exception as e:
        logging.error(f"Erro no carregamento de leads: {e}")
        raise


def airflow_load_propostas_parallel_task(**context) -> Dict[str, Any]:
    """Task de carregamento paralelo para propostas"""
    try:
        # Recupera dados da transformação de propostas
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='propostas_transformation_result')
        else:
            raise ValueError("Dados de transformação de propostas não encontrados")
        
        # Carrega dados de propostas
        if transformed_data['success'] and not transformed_data['transformed_data']['tb_propostas'].empty:
            result = process_etl_pipeline(transformed_data['transformed_data'], dry_run=False)
            logging.info(f"✅ Carregamento Propostas: {result.get('total_records', 0)} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_propostas'],
                'message': 'Nenhum dado de propostas para carregar'
            }
            logging.info("⚠️ Carregamento Propostas: Nenhum dado para carregar")
        
        return result
    except Exception as e:
        logging.error(f"Erro no carregamento de propostas: {e}")
        raise
