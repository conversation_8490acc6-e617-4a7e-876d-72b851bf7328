# =============================================================================
# EXAMPLE ENVIRONMENT VARIABLES - SALESFORCE INTEGRATION ETL
# =============================================================================
# 
# SECURITY NOTICE: This file contains examples only. 
# Copy this file to .env and fill in your actual credentials.
# NEVER commit real credentials to version control!
#
# =============================================================================

# =============================================================================
# DATABASE CREDENTIALS - REQUIRED
# =============================================================================

# NewCon Database
NEWCON_USER=your_newcon_username
NEWCON_PASSWORD=your_newcon_password

# Data Warehouse
DW_USER=your_dw_username
DW_PASSWORD=your_dw_password

# Orbbits Database
ORBBITS_USER=your_orbbits_username
ORBBITS_PASSWORD=your_orbbits_password

# Quiver Database
QUIVER_USER=your_quiver_username
QUIVER_PASSWORD=your_quiver_password

# =============================================================================
# SALESFORCE MARKETING CLOUD CREDENTIALS - REQUIRED
# =============================================================================

SALESFORCE_CLIENT_ID=your_salesforce_client_id
SALESFORCE_CLIENT_SECRET=your_salesforce_client_secret

# =============================================================================
# RD STATION CREDENTIALS - REQUIRED
# =============================================================================

RDSTATION_TOKEN=your_rdstation_token

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Environment
ENVIRONMENT=development
DEBUG=false
DRY_RUN=false

# Logging
LOG_LEVEL=INFO
LOG_CONSOLE_OUTPUT=true

# Performance
PARALLEL_EXECUTION=true
MAX_WORKERS=4
MEMORY_LIMIT_MB=2048

# Monitoring
MONITORING_PROGRESS_BAR=true
MONITORING_METRICS=true
MONITORING_ALERTS=true
MONITORING_ALERT_EMAIL=<EMAIL>
MONITORING_WEBHOOK_URL=https://hooks.slack.com/your/webhook/url

# Salesforce Configuration
SALESFORCE_VERIFY_STATUS=true
SALESFORCE_BATCH_SIZE=2000
SALESFORCE_TIMEOUT=300

# RD Station Configuration  
RDSTATION_RATE_LIMIT=180
RDSTATION_PAGE_SIZE=200
