# 🔧 Plano de Refatoração - ETL Salesforce Marketing Cloud

**Data da Análise**: 2025-01-17  
**Versão**: 1.0  
**Status**: Planejamento Aprovado  

## 📊 Resumo Executivo

### Situação Atual
- **Sistema Funcional**: ETL operacional integrando NewCon, RD Station, Orbbits e Quiver → Salesforce Marketing Cloud
- **Arquitetura Sólida**: Separação clara de responsabilidades, suporte a múltiplas unidades de negócio
- **Pontos Críticos Identificados**: 5 problemas críticos que impactam manutenibilidade e segurança

### Objetivo da Refatoração
Transformar o sistema atual em uma solução **enterprise-grade** mantendo toda funcionalidade existente, com foco em:
- 🔒 **Segurança**: Remoção de credenciais hardcoded
- 🏗️ **Manutenibilidade**: Modularização do código monolítico
- ⚡ **Performance**: Processamento paralelo real
- 🧪 **Confiabilidade**: Cobertura de testes automatizados
- 📊 **Observabilidade**: Monitoramento proativo

## 🎯 Problemas Críticos Identificados

### 1. **Arquivo Monolítico** - CRÍTICO 🚨
- **Arquivo**: `etl_main.py`
- **Problema**: 1.748 linhas, 45 funções em um único arquivo
- **Impacto**: Dificulta manutenção, debugging e colaboração em equipe
- **Risco**: Alto - Mudanças podem quebrar funcionalidades não relacionadas

### 2. **Credenciais Expostas** - CRÍTICO 🚨
- **Arquivos**: `config.py`, `business_units/*/config.py`
- **Problema**: Credenciais reais como valores default
- **Exemplos**:
  - Salesforce: `bneq1jhgjhuhsekg68zchecl` / `HcLyVCyU0ed48fKW6BGpGdff`
  - RD Station: `63dcebb7505962001bdfec12`
- **Risco**: Crítico - Exposição de credenciais em repositório

### 3. **Processamento Sequencial** - ALTO ⚠️
- **Arquivo**: `data_extractors.py:62`
- **Problema**: Comentário "Extração sequencial para evitar sobrecarga"
- **Impacto**: Performance subótima, tempo de execução desnecessariamente longo
- **Oportunidade**: Paralelização pode reduzir tempo de execução em 60-70%

### 4. **Ausência de Testes** - ALTO ⚠️
- **Cobertura Atual**: 0% - Nenhum teste automatizado
- **Impacto**: Risco alto de regressões, dificuldade para refatorar com segurança
- **Dependências**: `pytest` e `pytest-cov` já estão no requirements.txt

### 5. **Gestão de Memória Passiva** - MÉDIO ⚠️
- **Problema**: Função `get_memory_usage()` existe mas não é usada ativamente
- **Impacto**: Risco de OutOfMemory em datasets grandes (tb_propostas: 533k registros)

## 🗓️ Cronograma de Implementação

### **FASE 1: Segurança e Estrutura** (Semanas 1-2)
**Objetivo**: Resolver problemas críticos de segurança e estrutura

#### Semana 1: Refatoração Estrutural
- [ ] **Dia 1-2**: Criar nova estrutura de diretórios
- [ ] **Dia 3-4**: Mover funções do `etl_main.py` para módulos específicos
- [ ] **Dia 5**: Testes de integração e validação

#### Semana 2: Segurança de Credenciais
- [ ] **Dia 1-2**: Remover todos os defaults de credenciais
- [ ] **Dia 3**: Implementar validação obrigatória de variáveis de ambiente
- [ ] **Dia 4-5**: Testes de segurança e documentação

### **FASE 2: Performance e Paralelização** (Semanas 3-4)
**Objetivo**: Implementar processamento paralelo real

#### Semana 3: Extração Paralela
- [ ] **Dia 1-2**: Implementar `ThreadPoolExecutor` para extrações
- [ ] **Dia 3-4**: Implementar streaming para datasets grandes
- [ ] **Dia 5**: Testes de performance e benchmarks

#### Semana 4: Otimização de Memória
- [ ] **Dia 1-2**: Implementar monitoramento ativo de memória
- [ ] **Dia 3-4**: Implementar chunking para `pd.read_sql()`
- [ ] **Dia 5**: Validação e ajustes de performance

### **FASE 3: Testes e Confiabilidade** (Semanas 5-6)
**Objetivo**: Implementar cobertura de testes automatizados

#### Semana 5: Testes Unitários
- [ ] **Dia 1**: Setup da estrutura de testes (pytest, fixtures)
- [ ] **Dia 2-3**: Testes para `utils.py` e `data_transformers.py`
- [ ] **Dia 4-5**: Testes para `data_extractors.py`

#### Semana 6: Testes de Integração
- [ ] **Dia 1-2**: Testes para `salesforce_client.py`
- [ ] **Dia 3-4**: Testes de integração do pipeline completo
- [ ] **Dia 5**: Configuração de CI/CD com cobertura mínima

### **FASE 4: Monitoramento e Alertas** (Semanas 7-8)
**Objetivo**: Implementar observabilidade proativa

#### Semana 7: Sistema de Alertas
- [ ] **Dia 1-2**: Implementar `AlertManager` com Slack/Email
- [ ] **Dia 3-4**: Configurar thresholds e triggers
- [ ] **Dia 5**: Testes de alertas

#### Semana 8: Métricas Históricas
- [ ] **Dia 1-2**: Implementar persistência de métricas
- [ ] **Dia 3-4**: Dashboard básico de métricas
- [ ] **Dia 5**: Documentação final e handover

## 📁 Nova Estrutura de Diretórios

```
salesforce_integration/
├── core/                           # Componentes centrais (mantido)
│   ├── __init__.py
│   └── base_dag_factory.py
├── orchestrators/                  # NOVO - Pipeline principal
│   ├── __init__.py
│   ├── etl_pipeline.py            # Classe ETLPipeline
│   └── pipeline_executor.py       # CLI e execução
├── airflow_tasks/                  # NOVO - Tasks do Airflow
│   ├── __init__.py
│   ├── extraction_tasks.py        # airflow_extract_* functions
│   ├── transformation_tasks.py    # airflow_transform_* functions
│   └── loading_tasks.py           # airflow_load_* functions
├── reports/                        # NOVO - Relatórios
│   ├── __init__.py
│   └── execution_reporter.py      # Templates e relatórios
├── monitoring/                     # NOVO - Monitoramento
│   ├── __init__.py
│   ├── alert_manager.py           # Sistema de alertas
│   ├── metrics_collector.py       # Coleta de métricas
│   └── memory_monitor.py          # Monitoramento de memória
├── tests/                          # NOVO - Testes
│   ├── __init__.py
│   ├── conftest.py                # Fixtures pytest
│   ├── unit/
│   │   ├── test_data_extractors.py
│   │   ├── test_data_transformers.py
│   │   ├── test_salesforce_client.py
│   │   └── test_utils.py
│   ├── integration/
│   │   ├── test_database_connections.py
│   │   └── test_etl_pipeline.py
│   └── fixtures/
│       ├── sample_data.json
│       └── mock_responses.json
├── business_units/                 # Mantido
├── consultas/                      # Mantido
├── carga_fria/                     # Mantido
└── [arquivos existentes mantidos]
```

## 🔄 Estratégia de Migração

### Princípios da Refatoração
1. **Zero Downtime**: Sistema atual continua funcionando durante refatoração
2. **Backward Compatibility**: Manter compatibilidade com DAGs existentes
3. **Incremental**: Migração gradual, módulo por módulo
4. **Testável**: Cada mudança deve ser testável independentemente

### Abordagem de Implementação
1. **Criar Novos Módulos**: Implementar nova estrutura em paralelo
2. **Migrar Gradualmente**: Mover funções uma por vez
3. **Manter Imports**: Usar imports de compatibilidade temporários
4. **Validar Continuamente**: Testes após cada migração
5. **Deprecar Gradualmente**: Remover código antigo após validação

## 📋 Checklist de Validação

### Pré-Refatoração
- [ ] Backup completo do código atual
- [ ] Documentação do estado atual (este arquivo)
- [ ] Testes manuais de todas as funcionalidades
- [ ] Identificação de todas as dependências

### Durante a Refatoração
- [ ] Testes unitários para cada módulo refatorado
- [ ] Validação de compatibilidade com Airflow
- [ ] Verificação de performance (não deve degradar)
- [ ] Validação de segurança (credenciais protegidas)

### Pós-Refatoração
- [ ] Cobertura de testes > 80%
- [ ] Todas as DAGs funcionando
- [ ] Performance igual ou melhor
- [ ] Sistema de alertas operacional
- [ ] Documentação atualizada

## 🎯 Métricas de Sucesso

### Quantitativas
- **Cobertura de Testes**: 0% → 80%+
- **Linhas por Arquivo**: Máximo 500 linhas
- **Tempo de Execução**: Redução de 30-50% com paralelização
- **Uso de Memória**: Controle ativo com alertas
- **MTTR**: Redução do tempo de resolução de problemas

### Qualitativas
- **Manutenibilidade**: Código modular e bem documentado
- **Segurança**: Credenciais protegidas
- **Confiabilidade**: Sistema robusto com testes
- **Observabilidade**: Visibilidade completa do sistema
- **Escalabilidade**: Preparado para crescimento

## 📚 Recursos e Referências

### Documentação Técnica
- [CLAUDE.md](./CLAUDE.md) - Guia técnico atual
- [BUSINESS_UNITS_GUIDE.md](./BUSINESS_UNITS_GUIDE.md) - Arquitetura de unidades
- [requirements.txt](./requirements.txt) - Dependências

### Ferramentas Utilizadas
- **Testes**: pytest, pytest-cov
- **Qualidade**: black, flake8, isort
- **Monitoramento**: psutil, structlog
- **Paralelização**: concurrent.futures, ThreadPoolExecutor

## 🛠️ Detalhes Técnicos de Implementação

### Refatoração do `etl_main.py`

#### Mapeamento de Funções
```python
# ANTES: etl_main.py (1748 linhas)
# DEPOIS: Distribuído em múltiplos módulos

# orchestrators/etl_pipeline.py
class ETLPipeline:  # Linhas 60-334 do etl_main.py

# orchestrators/pipeline_executor.py
def main():  # Linhas 1688-1748
def parse_arguments():  # Linhas 1621-1687

# airflow_tasks/extraction_tasks.py
def airflow_extract_*():  # Linhas 340-830

# airflow_tasks/transformation_tasks.py
def airflow_transform_*():  # Linhas 929-1124

# airflow_tasks/loading_tasks.py
def airflow_load_*():  # Linhas 1125-1278

# reports/execution_reporter.py
def generate_execution_report():  # Linhas 1589-1616
def create_dag_template():  # Linhas 1509-1588
```

### Implementação de Processamento Paralelo

#### Extração Paralela
```python
# data_extractors.py - Nova implementação
import concurrent.futures
from threading import ThreadPoolExecutor

class DataExtractor:
    def extract_all_sources_parallel(self, sources: List[str] = None) -> Dict[str, pd.DataFrame]:
        """Extrai dados de todas as fontes em paralelo"""
        sources = sources or ['newcon', 'rdstation', 'orbbits', 'quiver']
        max_workers = min(len(sources), ENVIRONMENT_CONFIG['max_workers'])

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_source = {
                executor.submit(self._extract_source_safe, source): source
                for source in sources
            }

            results = {}
            for future in concurrent.futures.as_completed(future_to_source):
                source = future_to_source[future]
                try:
                    source_data = future.result(timeout=TIMEOUT_CONFIGS['database_query'])
                    results.update(source_data)
                    self.logger.info(f"✅ Extração paralela de {source} concluída")
                except Exception as e:
                    self.logger.error(f"❌ Erro na extração paralela de {source}: {e}")
                    # Continua com outras fontes

            return {'success': True, 'data': results}

    def _extract_source_safe(self, source: str) -> Dict[str, pd.DataFrame]:
        """Wrapper seguro para extração com tratamento de erro"""
        try:
            if source == 'newcon':
                return self._extract_newcon_all()
            elif source == 'rdstation':
                return {'rdstation_leads': self.extract_rdstation_leads()}
            elif source == 'orbbits':
                return self._extract_orbbits_all()
            elif source == 'quiver':
                return self._extract_quiver_all()
        except Exception as e:
            self.logger.error(f"Erro na extração de {source}: {e}")
            return {}
```

#### Streaming para Grandes Datasets
```python
# data_extractors.py - Implementação de chunking
def extract_with_chunking(self, query: str, chunk_size: int = None) -> Iterator[pd.DataFrame]:
    """Extrai dados em chunks para economizar memória"""
    chunk_size = chunk_size or BATCH_SIZES['memory_limit']

    try:
        with get_database_connection(self.db_name) as conn:
            for chunk_num, chunk in enumerate(pd.read_sql(query, conn, chunksize=chunk_size)):
                # Monitora uso de memória a cada chunk
                memory_usage = get_memory_usage()
                if memory_usage['rss_mb'] > ENVIRONMENT_CONFIG['memory_limit_mb'] * 0.8:
                    self.logger.warning(f"⚠️ Alto uso de memória: {memory_usage['rss_mb']:.1f}MB")

                self.logger.debug(f"Processando chunk {chunk_num + 1}: {len(chunk)} registros")
                yield chunk

    except Exception as e:
        self.logger.error(f"Erro na extração com chunking: {e}")
        raise
```

### Sistema de Monitoramento de Memória

#### Implementação do Memory Monitor
```python
# monitoring/memory_monitor.py
import psutil
from typing import Dict, Any, Optional
from salesforce_integration.config import ENVIRONMENT_CONFIG

class MemoryMonitor:
    def __init__(self, limit_mb: int = None, warning_threshold: float = 0.8):
        self.limit_mb = limit_mb or ENVIRONMENT_CONFIG['memory_limit_mb']
        self.warning_threshold = warning_threshold
        self.logger = logging.getLogger(__name__)

    def check_memory_usage(self) -> Dict[str, Any]:
        """Verifica uso atual de memória"""
        usage = get_memory_usage()

        status = {
            'usage_mb': usage['rss_mb'],
            'limit_mb': self.limit_mb,
            'percentage': (usage['rss_mb'] / self.limit_mb) * 100,
            'status': 'ok'
        }

        if usage['rss_mb'] > self.limit_mb:
            status['status'] = 'critical'
            raise MemoryError(f"Uso de memória excedeu limite: {usage['rss_mb']:.1f}MB > {self.limit_mb}MB")
        elif usage['rss_mb'] > self.limit_mb * self.warning_threshold:
            status['status'] = 'warning'
            self.logger.warning(f"⚠️ Alto uso de memória: {usage['rss_mb']:.1f}MB ({status['percentage']:.1f}%)")

        return status

    def monitor_function(self, func):
        """Decorator para monitorar uso de memória de uma função"""
        def wrapper(*args, **kwargs):
            initial_usage = get_memory_usage()
            try:
                result = func(*args, **kwargs)
                final_usage = get_memory_usage()

                memory_delta = final_usage['rss_mb'] - initial_usage['rss_mb']
                self.logger.info(f"📊 {func.__name__}: Δ memória = {memory_delta:+.1f}MB")

                self.check_memory_usage()
                return result
            except MemoryError:
                self.logger.error(f"❌ {func.__name__}: Limite de memória excedido")
                raise
        return wrapper
```

### Sistema de Alertas

#### Alert Manager
```python
# monitoring/alert_manager.py
import requests
import smtplib
from email.mime.text import MIMEText
from typing import Dict, Any, Optional

class AlertManager:
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.slack_webhook = self.config.get('slack_webhook_url')
        self.email_config = self.config.get('email', {})
        self.logger = logging.getLogger(__name__)

    def send_failure_alert(self, pipeline_result: Dict[str, Any]):
        """Envia alerta de falha no pipeline"""
        if not pipeline_result.get('success', True):
            alert_data = self._prepare_alert_data(pipeline_result)

            if self.slack_webhook:
                self._send_slack_alert(alert_data)

            if self.email_config:
                self._send_email_alert(alert_data)

    def _prepare_alert_data(self, pipeline_result: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara dados do alerta"""
        stats = pipeline_result.get('execution_stats', {})
        failure_summary = pipeline_result.get('failure_summary', {})

        return {
            'title': '🚨 ETL Pipeline Failure',
            'environment': ENVIRONMENT_CONFIG['env'],
            'timestamp': datetime.now().isoformat(),
            'duration': f"{stats.get('total_time', 0):.1f}s",
            'error': pipeline_result.get('error', 'Unknown error'),
            'failed_tables': failure_summary.get('failed_tables', []),
            'total_failures': failure_summary.get('total_failures', 0)
        }

    def _send_slack_alert(self, alert_data: Dict[str, Any]):
        """Envia alerta para Slack"""
        try:
            payload = {
                "text": f"{alert_data['title']} - {alert_data['environment']}",
                "attachments": [{
                    "color": "danger",
                    "fields": [
                        {"title": "Environment", "value": alert_data['environment'], "short": True},
                        {"title": "Duration", "value": alert_data['duration'], "short": True},
                        {"title": "Error", "value": alert_data['error'], "short": False},
                        {"title": "Failed Tables", "value": ", ".join(alert_data['failed_tables']), "short": False}
                    ],
                    "ts": int(datetime.now().timestamp())
                }]
            }

            response = requests.post(self.slack_webhook, json=payload, timeout=10)
            if response.status_code == 200:
                self.logger.info("✅ Alerta Slack enviado com sucesso")
            else:
                self.logger.error(f"❌ Erro ao enviar alerta Slack: {response.status_code}")

        except Exception as e:
            self.logger.error(f"❌ Erro ao enviar alerta Slack: {e}")
```

### Estrutura de Testes

#### Configuração Base
```python
# tests/conftest.py
import pytest
import pandas as pd
from unittest.mock import Mock, patch
from salesforce_integration.test_data_manager import generate_test_data

@pytest.fixture
def sample_clients_data():
    """Fixture com dados de clientes para testes"""
    return generate_test_data('tb_clientes', 10)

@pytest.fixture
def mock_database_connection():
    """Mock para conexões de banco de dados"""
    with patch('salesforce_integration.database_connections.get_database_connection') as mock:
        mock_conn = Mock()
        mock.__enter__ = Mock(return_value=mock_conn)
        mock.__exit__ = Mock(return_value=None)
        yield mock_conn

@pytest.fixture
def mock_salesforce_client():
    """Mock para cliente Salesforce"""
    with patch('salesforce_integration.salesforce_client.SalesforceClient') as mock:
        client = Mock()
        client.authenticate.return_value = True
        client.process_table_data.return_value = {'success': True, 'total_records': 100}
        mock.return_value = client
        yield client
```

#### Testes Unitários Exemplo
```python
# tests/unit/test_data_extractors.py
import pytest
from unittest.mock import patch, Mock
from salesforce_integration.data_extractors import DataExtractor, NewConExtractor

class TestDataExtractor:
    def test_extract_all_sources_parallel_success(self, mock_database_connection):
        """Testa extração paralela com sucesso"""
        extractor = DataExtractor()

        with patch.object(extractor, '_extract_source_safe') as mock_extract:
            mock_extract.side_effect = [
                {'newcon_clients': pd.DataFrame({'id': [1, 2]})},
                {'rdstation_leads': pd.DataFrame({'id': [3, 4]})},
                {'orbbits_origin': pd.DataFrame({'id': [5, 6]})},
                {'quiver_clients': pd.DataFrame({'id': [7, 8]})}
            ]

            result = extractor.extract_all_sources_parallel(['newcon', 'rdstation', 'orbbits', 'quiver'])

            assert result['success'] is True
            assert len(result['data']) == 4
            assert 'newcon_clients' in result['data']
            assert 'rdstation_leads' in result['data']

    def test_extract_with_chunking(self, mock_database_connection):
        """Testa extração com chunking"""
        extractor = NewConExtractor()

        # Mock pd.read_sql com chunksize
        mock_chunks = [
            pd.DataFrame({'id': [1, 2], 'name': ['A', 'B']}),
            pd.DataFrame({'id': [3, 4], 'name': ['C', 'D']})
        ]

        with patch('pandas.read_sql', return_value=iter(mock_chunks)):
            chunks = list(extractor.extract_with_chunking("SELECT * FROM test", chunk_size=2))

            assert len(chunks) == 2
            assert len(chunks[0]) == 2
            assert len(chunks[1]) == 2

class TestMemoryMonitor:
    def test_memory_check_normal(self):
        """Testa verificação de memória em condições normais"""
        monitor = MemoryMonitor(limit_mb=2048)

        with patch('salesforce_integration.utils.get_memory_usage') as mock_usage:
            mock_usage.return_value = {'rss_mb': 1000, 'vms_mb': 1200, 'percent': 10.5}

            status = monitor.check_memory_usage()

            assert status['status'] == 'ok'
            assert status['usage_mb'] == 1000
            assert status['percentage'] < 50

    def test_memory_check_critical(self):
        """Testa verificação de memória em condições críticas"""
        monitor = MemoryMonitor(limit_mb=1024)

        with patch('salesforce_integration.utils.get_memory_usage') as mock_usage:
            mock_usage.return_value = {'rss_mb': 2048, 'vms_mb': 2500, 'percent': 25.0}

            with pytest.raises(MemoryError):
                monitor.check_memory_usage()
```

## 🚀 Comandos Práticos para Implementação

### Setup Inicial
```bash
# 1. Criar backup do estado atual
git checkout -b backup/pre-refactoring
git add -A && git commit -m "Backup antes da refatoração"

# 2. Criar branch de desenvolvimento
git checkout -b feature/refactoring-phase-1

# 3. Criar estrutura de diretórios
mkdir -p orchestrators airflow_tasks reports monitoring tests/{unit,integration,fixtures}

# 4. Instalar dependências de desenvolvimento
pip install pytest pytest-cov black flake8 isort
```

### Fase 1: Comandos de Refatoração Estrutural
```bash
# Semana 1: Criar novos módulos
touch orchestrators/{__init__.py,etl_pipeline.py,pipeline_executor.py}
touch airflow_tasks/{__init__.py,extraction_tasks.py,transformation_tasks.py,loading_tasks.py}
touch reports/{__init__.py,execution_reporter.py}

# Mover funções específicas (exemplo)
# Extrair classe ETLPipeline (linhas 60-334)
sed -n '60,334p' etl_main.py > orchestrators/etl_pipeline.py

# Validar sintaxe após cada mudança
python -m py_compile orchestrators/etl_pipeline.py
```

### Fase 2: Comandos de Segurança
```bash
# Remover credenciais hardcoded
sed -i "s/default='bneq1jhgjhuhsekg68zchecl'/# REMOVED: default credential/" config.py
sed -i "s/default='HcLyVCyU0ed48fKW6BGpGdff'/# REMOVED: default credential/" config.py

# Validar que não há mais credenciais
grep -r "bneq1jhgjhuhsekg68zchecl" . || echo "✅ Credencial removida"
grep -r "63dcebb7505962001bdfec12" . || echo "✅ Token RD removido"
```

### Fase 3: Comandos de Testes
```bash
# Configurar pytest
cat > pytest.ini << EOF
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = --cov=salesforce_integration --cov-report=html --cov-report=term-missing
EOF

# Executar testes
pytest tests/ -v
pytest --cov=salesforce_integration --cov-report=html
```

### Comandos de Validação
```bash
# Verificar qualidade de código
black --check .
flake8 --max-line-length=120 .
isort --check-only .

# Executar pipeline de teste
python -m salesforce_integration.orchestrators.pipeline_executor --dry-run --test-sample=10

# Validar DAGs do Airflow
python -c "from airflow.models import DagBag; db = DagBag(); print('✅ DAGs válidas' if not db.import_errors else f'❌ Erros: {db.import_errors}')"
```

## 📋 Checklist Detalhado de Implementação

### ✅ Fase 1: Segurança e Estrutura (Semanas 1-2)

#### Semana 1: Refatoração Estrutural
- [ ] **Dia 1**: Criar estrutura de diretórios
  - [ ] `mkdir -p orchestrators airflow_tasks reports monitoring tests/{unit,integration,fixtures}`
  - [ ] Criar todos os `__init__.py`
  - [ ] Validar imports básicos

- [ ] **Dia 2**: Extrair classe ETLPipeline
  - [ ] Mover linhas 60-334 de `etl_main.py` → `orchestrators/etl_pipeline.py`
  - [ ] Ajustar imports e dependências
  - [ ] Testar instanciação da classe

- [ ] **Dia 3**: Extrair funções de extração
  - [ ] Mover funções `airflow_extract_*` → `airflow_tasks/extraction_tasks.py`
  - [ ] Criar imports de compatibilidade em `etl_main.py`
  - [ ] Validar que DAGs ainda funcionam

- [ ] **Dia 4**: Extrair funções de transformação e carregamento
  - [ ] Mover `airflow_transform_*` → `airflow_tasks/transformation_tasks.py`
  - [ ] Mover `airflow_load_*` → `airflow_tasks/loading_tasks.py`
  - [ ] Testar execução de tasks individuais

- [ ] **Dia 5**: Extrair relatórios e CLI
  - [ ] Mover `generate_execution_report` → `reports/execution_reporter.py`
  - [ ] Mover `main()` e `parse_arguments()` → `orchestrators/pipeline_executor.py`
  - [ ] Testar execução via linha de comando

#### Semana 2: Segurança de Credenciais
- [ ] **Dia 1**: Remover defaults de credenciais
  - [ ] Remover defaults em `config.py`
  - [ ] Remover defaults em `business_units/*/config.py`
  - [ ] Adicionar validação obrigatória

- [ ] **Dia 2**: Implementar validação de segurança
  - [ ] Completar função `validate_configuration()`
  - [ ] Adicionar verificação de credenciais padrão
  - [ ] Implementar falha rápida se credenciais ausentes

- [ ] **Dia 3**: Testes de segurança
  - [ ] Verificar que sistema falha sem credenciais
  - [ ] Testar com credenciais via variáveis de ambiente
  - [ ] Validar mascaramento de dados sensíveis

- [ ] **Dia 4**: Documentação de segurança
  - [ ] Atualizar `README.md` com variáveis obrigatórias
  - [ ] Criar guia de configuração de ambiente
  - [ ] Documentar processo de deploy seguro

- [ ] **Dia 5**: Validação final Fase 1
  - [ ] Executar pipeline completo em ambiente de teste
  - [ ] Validar todas as DAGs do Airflow
  - [ ] Confirmar que não há regressões funcionais

### ✅ Fase 2: Performance e Paralelização (Semanas 3-4)

#### Semana 3: Extração Paralela
- [ ] **Dia 1**: Implementar ThreadPoolExecutor
  - [ ] Criar `extract_all_sources_parallel()` em `DataExtractor`
  - [ ] Implementar `_extract_source_safe()` com tratamento de erro
  - [ ] Configurar `max_workers` baseado em `ENVIRONMENT_CONFIG`

- [ ] **Dia 2**: Testes de paralelização
  - [ ] Benchmark: extração sequencial vs paralela
  - [ ] Validar que dados extraídos são idênticos
  - [ ] Testar com diferentes números de workers

- [ ] **Dia 3**: Implementar chunking para datasets grandes
  - [ ] Criar `extract_with_chunking()` com `pd.read_sql(chunksize=...)`
  - [ ] Implementar processamento incremental
  - [ ] Testar com tabela `tb_propostas` (533k registros)

- [ ] **Dia 4**: Otimizar uso de memória
  - [ ] Implementar liberação de memória entre chunks
  - [ ] Adicionar garbage collection forçado quando necessário
  - [ ] Testar limites de memória

- [ ] **Dia 5**: Integração e testes
  - [ ] Integrar extração paralela no pipeline principal
  - [ ] Testes de stress com múltiplas fontes
  - [ ] Validar que não há race conditions

#### Semana 4: Monitoramento de Memória
- [ ] **Dia 1**: Implementar MemoryMonitor
  - [ ] Criar classe `MemoryMonitor` em `monitoring/memory_monitor.py`
  - [ ] Implementar `check_memory_usage()` com thresholds
  - [ ] Criar decorator `monitor_function()`

- [ ] **Dia 2**: Integrar monitoramento no pipeline
  - [ ] Adicionar verificações de memória em pontos críticos
  - [ ] Implementar alertas de memória
  - [ ] Testar com diferentes limites

- [ ] **Dia 3**: Implementar AlertManager básico
  - [ ] Criar `AlertManager` em `monitoring/alert_manager.py`
  - [ ] Implementar alertas via log (preparação para Slack/Email)
  - [ ] Integrar com sistema de falhas existente

- [ ] **Dia 4**: Testes de monitoramento
  - [ ] Simular condições de alta memória
  - [ ] Testar alertas e recovery
  - [ ] Validar que sistema não trava com OOM

- [ ] **Dia 5**: Validação final Fase 2
  - [ ] Benchmark completo: antes vs depois
  - [ ] Documentar melhorias de performance
  - [ ] Confirmar estabilidade do sistema

### ✅ Fase 3: Testes e Confiabilidade (Semanas 5-6)

#### Semana 5: Testes Unitários
- [ ] **Dia 1**: Setup de testes
  - [ ] Configurar `pytest.ini` e `conftest.py`
  - [ ] Criar fixtures básicas (dados de teste, mocks)
  - [ ] Configurar cobertura de código

- [ ] **Dia 2**: Testes para `utils.py`
  - [ ] Testar todas as funções de validação (CPF, email, telefone)
  - [ ] Testar formatação de dados
  - [ ] Testar utilitários de progresso e métricas

- [ ] **Dia 3**: Testes para `data_transformers.py`
  - [ ] Testar transformações principais (`transform_clients`, `transform_leads`)
  - [ ] Testar validações de qualidade de dados
  - [ ] Testar casos edge (dados vazios, inválidos)

- [ ] **Dia 4**: Testes para `data_extractors.py`
  - [ ] Testar extractors individuais com mocks
  - [ ] Testar extração paralela
  - [ ] Testar chunking e streaming

- [ ] **Dia 5**: Testes para novos módulos
  - [ ] Testar `MemoryMonitor`
  - [ ] Testar `AlertManager`
  - [ ] Testar `ETLPipeline` refatorado

#### Semana 6: Testes de Integração
- [ ] **Dia 1**: Testes para `salesforce_client.py`
  - [ ] Testar autenticação (com mocks)
  - [ ] Testar envio de dados em lotes
  - [ ] Testar tratamento de erros da API

- [ ] **Dia 2**: Testes de integração de banco
  - [ ] Testar conexões com bancos (usando testcontainers ou mocks)
  - [ ] Testar queries SQL
  - [ ] Testar failover de conexões

- [ ] **Dia 3**: Testes de pipeline completo
  - [ ] Testar pipeline end-to-end com dados de teste
  - [ ] Testar diferentes cenários (sucesso, falha parcial, falha total)
  - [ ] Testar modo dry-run

- [ ] **Dia 4**: Configurar CI/CD
  - [ ] Configurar GitHub Actions ou similar
  - [ ] Definir cobertura mínima (80%)
  - [ ] Configurar quality gates

- [ ] **Dia 5**: Validação final Fase 3
  - [ ] Executar toda suite de testes
  - [ ] Gerar relatório de cobertura
  - [ ] Confirmar que cobertura > 80%

### ✅ Fase 4: Monitoramento e Alertas (Semanas 7-8)

#### Semana 7: Sistema de Alertas
- [ ] **Dia 1**: Implementar alertas Slack
  - [ ] Completar `_send_slack_alert()` no `AlertManager`
  - [ ] Configurar webhook do Slack
  - [ ] Testar envio de alertas

- [ ] **Dia 2**: Implementar alertas por email
  - [ ] Completar `_send_email_alert()` no `AlertManager`
  - [ ] Configurar SMTP
  - [ ] Testar envio de emails

- [ ] **Dia 3**: Configurar thresholds e triggers
  - [ ] Definir quando enviar alertas (falhas, performance, memória)
  - [ ] Implementar rate limiting de alertas
  - [ ] Configurar diferentes níveis de severidade

- [ ] **Dia 4**: Integrar alertas no pipeline
  - [ ] Adicionar alertas em pontos críticos
  - [ ] Testar alertas em cenários reais
  - [ ] Configurar alertas para diferentes ambientes

- [ ] **Dia 5**: Testes de alertas
  - [ ] Simular falhas e verificar alertas
  - [ ] Testar rate limiting
  - [ ] Validar formato e conteúdo dos alertas

#### Semana 8: Métricas e Finalização
- [ ] **Dia 1**: Implementar persistência de métricas
  - [ ] Criar `MetricsCollector` para salvar métricas históricas
  - [ ] Implementar storage em arquivo/banco
  - [ ] Configurar rotação de logs de métricas

- [ ] **Dia 2**: Dashboard básico
  - [ ] Criar script para gerar relatório de métricas
  - [ ] Implementar visualizações básicas (ASCII charts)
  - [ ] Configurar relatórios periódicos

- [ ] **Dia 3**: Documentação final
  - [ ] Atualizar `README.md` com nova estrutura
  - [ ] Documentar novas funcionalidades
  - [ ] Criar guia de troubleshooting

- [ ] **Dia 4**: Testes finais
  - [ ] Executar pipeline completo em ambiente de produção
  - [ ] Validar todas as funcionalidades
  - [ ] Confirmar que não há regressões

- [ ] **Dia 5**: Handover e deploy
  - [ ] Apresentar resultados da refatoração
  - [ ] Treinar equipe nas novas funcionalidades
  - [ ] Planejar deploy em produção

## 📊 Métricas de Acompanhamento

### Métricas Técnicas
- **Cobertura de Testes**: Target 80%+
- **Complexidade Ciclomática**: Máximo 10 por função
- **Linhas por Arquivo**: Máximo 500 linhas
- **Tempo de Execução**: Baseline vs Otimizado
- **Uso de Memória**: Peak e média

### Métricas de Qualidade
- **Code Smells**: Zero após refatoração
- **Duplicação de Código**: < 3%
- **Debt Ratio**: < 5%
- **Maintainability Index**: > 80

### Métricas de Confiabilidade
- **MTBF**: Mean Time Between Failures
- **MTTR**: Mean Time To Recovery
- **Success Rate**: % de execuções bem-sucedidas
- **Alert Response Time**: Tempo para resposta a alertas

---

**Status**: 📋 Plano Aprovado - Pronto para Implementação
**Próximos Passos**: Iniciar Fase 1 - Refatoração Estrutural
**Responsável**: Equipe de Desenvolvimento
**Revisão**: Semanal durante implementação
**Última Atualização**: 2025-01-17
