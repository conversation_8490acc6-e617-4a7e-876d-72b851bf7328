#!/usr/bin/env python3
"""
ETL Consolidado - Orquestrador Principal
Pipeline ETL principal para integração Salesforce Marketing Cloud.

RESPONSABILIDADES:
- Orquestrar extração, transformação e carregamento
- Suportar execução completa e seletiva
- Implementar modo dry-run
- Preparar para integração futura com Airflow
- Suportar argumentos de linha de comando

SEQUÊNCIA DE PROCESSAMENTO:
1. Produtos (tb_produtos)
2. Clientes (tb_clientes)  
3. Leads (tb_leads)
4. Propostas (tb_propostas)
"""

import sys
import time
import argparse
import logging
import os
import pandas as pd
import glob
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

# Importa todos os módulos do ETL
from salesforce_integration.config import *
from salesforce_integration.utils import setup_logging, ProgressTracker, mask_sensitive_data
from salesforce_integration.database_connections import test_all_connections
from salesforce_integration.data_extractors import DataExtractor
from salesforce_integration.data_transformers import (
    transform_all_data,
    transform_produtos_only,
    transform_clientes_only,
    transform_leads_only,
    transform_propostas_only
)
from salesforce_integration.salesforce_client import (
    create_salesforce_client,
    process_etl_pipeline,
    validate_salesforce_config,
    test_salesforce_connection
)


# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = setup_logging()

# =============================================================================
# CLASSE PRINCIPAL DO ETL
# =============================================================================

class ETLPipeline:
    """Orquestrador principal do pipeline ETL"""
    
    def __init__(self, dry_run: bool = False, test_sample: int = None):
        self.dry_run = dry_run
        self.test_sample = test_sample
        self.logger = logging.getLogger(__name__)
        self.execution_stats = {
            'start_time': None,
            'end_time': None,
            'total_records': 0,
            'tables_processed': [],
            'errors': []
        }

        # Componentes do pipeline
        self.extractor = DataExtractor(test_sample=test_sample)
        self.sf_client = None

        sample_info = f" (amostra: {test_sample})" if test_sample else ""
        self.logger.info(f"ETL Pipeline inicializado (dry_run={dry_run}){sample_info}")
    
    def validate_prerequisites(self) -> bool:
        """Valida pré-requisitos antes da execução"""
        self.logger.info("Validando pré-requisitos...")
        
        # Valida configuração do Salesforce
        is_valid, missing_fields = validate_salesforce_config()
        if not is_valid:
            self.logger.error(f"❌ Configuração Salesforce inválida: {missing_fields}")
            return False
        
        # Testa conexões de banco de dados
        if not test_all_connections():
            self.logger.error("❌ Falha na conexão com bancos de dados")
            return False
        
        # Testa conexão com Salesforce (se não for dry-run)
        if not self.dry_run:
            if not test_salesforce_connection():
                self.logger.error("❌ Falha na conexão com Salesforce")
                return False
        
        self.logger.info("✅ Todos os pré-requisitos validados")
        return True
    
    def extract_data(self, table_filter: Optional[List[str]] = None) -> Dict[str, Any]:
        """Extrai dados de todas as fontes"""
        self.logger.info("Iniciando extração de dados...")
        extraction_start = time.time()
        
        try:
            # Extrai dados de todas as fontes
            extraction_result = self.extractor.extract_all_sources()
            
            if not extraction_result['success']:
                self.logger.error("❌ Falha na extração de dados")
                return {'success': False, 'error': 'Falha na extração'}
            
            # Registra métricas de extração por fonte
            extraction_time = (time.time() - extraction_start) / 60
            for table_name, df in extraction_result['data'].items():
                records_count = len(df) if hasattr(df, '__len__') else 0
                
                # Log de volumes suspeitos
                if records_count == 0:
                    self.logger.warning(f"⚠️ Extração de {table_name} retornou 0 registros")
            
            # Filtra tabelas se especificado
            if table_filter:
                filtered_data = {}
                for table in table_filter:
                    if table in extraction_result['data']:
                        filtered_data[table] = extraction_result['data'][table]
                    else:
                        self.logger.warning(f"⚠️ Tabela '{table}' não encontrada nos dados extraídos")
                
                extraction_result['data'] = filtered_data
            
            self.logger.info(f"✅ Extração concluída: {extraction_result['total_records']} registros")
            return extraction_result
            
        except Exception as e:
            self.logger.error(f"❌ Erro na extração: {e}")
            return {'success': False, 'error': str(e)}
    
    def transform_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transforma dados extraídos"""
        self.logger.info("Iniciando transformação de dados...")
        transformation_start = time.time()
        
        try:
            # Conta registros antes da transformação
            input_counts = {}
            for table_name, df in raw_data.items():
                input_counts[table_name] = len(df) if hasattr(df, '__len__') else 0
            
            # Transforma dados usando o módulo de transformação
            transformation_result = transform_all_data(raw_data)
            
            if not transformation_result['success']:
                self.logger.error("❌ Falha na transformação de dados")
                return {'success': False, 'error': 'Falha na transformação'}
            
            # Verifica perda de registros na transformação
            transformation_time = (time.time() - transformation_start) / 60
            for table_name, df in transformation_result['transformed_data'].items():
                output_count = len(df) if hasattr(df, '__len__') else 0
                
                # Estima input baseado em mapeamentos conhecidos
                estimated_input = 0
                if table_name == 'tb_clientes':
                    estimated_input = input_counts.get('newcon_clients', 0)
                elif table_name == 'tb_produtos':
                    estimated_input = input_counts.get('newcon_products', 0)
                elif table_name == 'tb_propostas':
                    estimated_input = input_counts.get('newcon_proposals', 0)
                elif table_name == 'tb_leads':
                    estimated_input = input_counts.get('newcon_leads', 0) + input_counts.get('rdstation_leads', 0)
                
                # Log de perda significativa de registros
                if estimated_input > 0 and output_count < estimated_input * 0.8:  # Mais de 20% de perda
                    loss_percentage = (estimated_input - output_count) / estimated_input
                    self.logger.warning(f"⚠️ {loss_percentage:.0%} perda de registros na transformação de {table_name} ({output_count:,} vs {estimated_input:,})")
                
                # Detecta campos obrigatórios nulos
                if hasattr(df, 'isnull'):
                    required_fields = {
                        'tb_clientes': ['cnpjcpf', 'email'],
                        'tb_leads': ['cnpjcpf', 'dt_simulacao'], 
                        'tb_produtos': ['id_produto'],
                        'tb_propostas': ['idproposta', 'email']
                    }.get(table_name, [])
                    
                    for field in required_fields:
                        if field in df.columns:
                            null_count = df[field].isnull().sum()
                            if null_count > 0:
                                self.logger.warning(f"⚠️ Campo obrigatório '{field}' com {null_count} valores nulos em {table_name}")
            
            self.logger.info(f"✅ Transformação concluída: {transformation_result['total_records']} registros")
            return transformation_result
            
        except Exception as e:
            self.logger.error(f"❌ Erro na transformação: {e}")
            return {'success': False, 'error': str(e)}
    
    def load_data(self, transformed_data: Dict[str, List[Dict[str, Any]]], 
                  table_sequence: Optional[List[str]] = None) -> Dict[str, Any]:
        """Carrega dados no Salesforce"""
        self.logger.info("Iniciando carregamento de dados...")
        loading_start = time.time()
        
        try:
            # Carrega dados usando o cliente Salesforce
            load_result = process_etl_pipeline(
                tables_data=transformed_data,
                dry_run=self.dry_run,
                table_sequence=table_sequence
            )
            
            if not load_result['success']:
                self.logger.error("❌ Falha no carregamento de dados")
                return {'success': False, 'error': 'Falha no carregamento'}
            
            # Registra métricas de carregamento e detecta falhas
            loading_time = (time.time() - loading_start) / 60
            processing_summary = load_result.get('processing_summary', {})
            
            for table_name, table_result in processing_summary.items():
                total_records = table_result.get('total_records', 0)
                failed_batches = table_result.get('failed_batches', 0)
                errors = table_result.get('errors', [])
                
                # Log de métricas básicas
                self.logger.info(f"📊 {table_name}: {total_records} registros, {failed_batches} lotes falharam")
                
                # Log de falhas específicas do Salesforce
                if not table_result.get('success', True):
                    self.logger.error(f"❌ {table_name}: {failed_batches} lotes falharam de {table_result.get('total_batches', 0)}")
                    if errors:
                        self.logger.error(f"Primeiros erros: {errors[:3]}")
            
            self.logger.info(f"✅ Carregamento concluído: {load_result['total_records']} registros")
            return load_result
            
        except Exception as e:
            self.logger.error(f"❌ Erro no carregamento: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_pipeline(self, table_filter: Optional[List[str]] = None,
                    table_sequence: Optional[List[str]] = None) -> Dict[str, Any]:
        """Executa pipeline ETL completo"""
        self.execution_stats['start_time'] = time.time()
        
        self.logger.info("=" * 60)
        self.logger.info("INICIANDO PIPELINE ETL CONSOLIDADO")
        self.logger.info("=" * 60)
        
        # Valida pré-requisitos
        if not self.validate_prerequisites():
            return {'success': False, 'error': 'Falha na validação de pré-requisitos'}
        
        # Extrai dados
        extraction_result = self.extract_data(table_filter)
        if not extraction_result['success']:
            return extraction_result
        
        # Transforma dados
        transformation_result = self.transform_data(extraction_result['data'])
        if not transformation_result['success']:
            return transformation_result
        
        # Carrega dados
        load_result = self.load_data(
            transformation_result['transformed_data'],
            table_sequence
        )
        if not load_result['success']:
            return load_result
        
        # Estatísticas finais
        self.execution_stats['end_time'] = time.time()
        self.execution_stats['total_time'] = (
            self.execution_stats['end_time'] - self.execution_stats['start_time']
        )
        self.execution_stats['total_records'] = load_result['total_records']
        self.execution_stats['tables_processed'] = load_result['tables_processed']
        
        # Gera relatório de falhas
        failure_report = self.failure_reporter.generate_report()
        
        # Log final
        self.logger.info("=" * 60)
        self.logger.info("PIPELINE ETL CONCLUÍDO")
        self.logger.info("=" * 60)
        self.logger.info(f"Tempo total: {self.execution_stats['total_time']:.2f}s")
        self.logger.info(f"Registros processados: {self.execution_stats['total_records']}")
        self.logger.info(f"Tabelas processadas: {len(self.execution_stats['tables_processed'])}")
        
        # Exibe relatório de falhas se houver problemas
        if self.failure_reporter.failures:
            self.logger.warning("⚠️ PROBLEMAS ENCONTRADOS DURANTE A EXECUÇÃO:")
            for line in failure_report.split('\n'):
                if line.strip():
                    self.logger.warning(line)
        else:
            self.logger.info("✅ Nenhum problema detectado durante a execução")
        
        return {
            'success': True,
            'execution_stats': self.execution_stats,
            'extraction_result': extraction_result,
            'transformation_result': transformation_result,
            'load_result': load_result,
            'failure_report': failure_report,
            'failure_summary': self.failure_reporter.get_failure_summary()
        }
    
    def run_single_table(self, table_name: str) -> Dict[str, Any]:
        """Executa pipeline para uma única tabela"""
        self.logger.info(f"Executando pipeline para tabela: {table_name}")
        
        return self.run_pipeline(table_filter=[table_name])
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas da execução"""
        return self.execution_stats
    
    def cleanup(self):
        """Limpa recursos"""
        if self.sf_client:
            self.sf_client.close()
        
        self.logger.info("Pipeline finalizado")

# =============================================================================
# FUNÇÕES PARA INTEGRAÇÃO COM AIRFLOW
# =============================================================================

def airflow_extract_task(**context) -> Dict[str, Any]:
    """Task de extração para Airflow"""
    extractor = DataExtractor()
    
    try:
        result = extractor.extract_all_sources()
        
        # Salva resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='extraction_result', value=result)
        
        return result
    except Exception as e:
        logging.error(f"Erro na task de extração: {e}")
        raise

# =============================================================================
# FUNÇÕES DE EXTRAÇÃO PARALELA POR FONTE
# =============================================================================

def airflow_extract_newcon_task(**context) -> Dict[str, Any]:
    """Task de extração NewCon para Airflow"""
    extractor = DataExtractor()
    
    try:
        # Extrai apenas dados do NewCon
        result = extractor.extract_all_sources(sources=['newcon'])
        
        # Salva resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='newcon_extraction_result', value=result)
        
        logging.info(f"✅ Extração NewCon concluída: {sum(len(df) for df in result.values())} registros")
        return result
    except Exception as e:
        logging.error(f"Erro na task de extração NewCon: {e}")
        raise

def airflow_extract_rdstation_task(**context) -> Dict[str, Any]:
    """Task de extração RD Station para Airflow"""
    extractor = DataExtractor()
    
    try:
        # Extrai apenas dados do RD Station
        result = extractor.extract_all_sources(sources=['rdstation'])
        
        # Salva resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='rdstation_extraction_result', value=result)
        
        logging.info(f"✅ Extração RD Station concluída: {sum(len(df) for df in result.values())} registros")
        return result
    except Exception as e:
        logging.error(f"Erro na task de extração RD Station: {e}")
        raise

def airflow_extract_orbbits_task(**context) -> Dict[str, Any]:
    """Task de extração Orbbits para Airflow"""
    extractor = DataExtractor()
    
    try:
        # Extrai apenas dados do Orbbits
        result = extractor.extract_all_sources(sources=['orbbits'])
        
        # Salva resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='orbbits_extraction_result', value=result)
        
        logging.info(f"✅ Extração Orbbits concluída: {sum(len(df) for df in result.values())} registros")
        return result
    except Exception as e:
        logging.error(f"Erro na task de extração Orbbits: {e}")
        raise

def airflow_extract_quiver_task(**context) -> Dict[str, Any]:
    """Task de extração Quiver para Airflow"""
    extractor = DataExtractor()

    try:
        # Extrai apenas dados do Quiver
        result = extractor.extract_all_sources(sources=['quiver'])

        # Salva resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='quiver_extraction_result', value=result)

        logging.info(f"✅ Extração Quiver concluída: {sum(len(df) for df in result.values())} registros")
        return result
    except Exception as e:
        logging.error(f"Erro na task de extração Quiver: {e}")
        raise

# =============================================================================
# FUNÇÕES DE EXTRAÇÃO GRANULAR POR TABELA
# =============================================================================

def airflow_extract_newcon_clients_task(**context) -> Dict[str, Any]:
    """Task de extração específica para clientes NewCon"""
    extractor = DataExtractor()
    
    try:
        result = {'newcon_clients': extractor.extract_newcon_clients()}
        
        if 'ti' in context:
            context['ti'].xcom_push(key='newcon_clients_result', value=result)
        
        logging.info(f"✅ Extração NewCon Clientes: {len(result['newcon_clients'])} registros")
        return result
    except Exception as e:
        logging.error(f"Erro na extração NewCon Clientes: {e}")
        raise

def airflow_extract_newcon_products_task(**context) -> Dict[str, Any]:
    """Task de extração específica para produtos NewCon"""
    extractor = DataExtractor()
    
    try:
        result = {'newcon_products': extractor.extract_newcon_products()}
        
        if 'ti' in context:
            context['ti'].xcom_push(key='newcon_products_result', value=result)
        
        logging.info(f"✅ Extração NewCon Produtos: {len(result['newcon_products'])} registros")
        return result
    except Exception as e:
        logging.error(f"Erro na extração NewCon Produtos: {e}")
        raise

def airflow_extract_newcon_proposals_task(**context) -> Dict[str, Any]:
    """Task de extração específica para propostas NewCon"""
    extractor = DataExtractor()
    
    try:
        result = {'newcon_proposals': extractor.extract_newcon_proposals()}
        
        if 'ti' in context:
            context['ti'].xcom_push(key='newcon_proposals_result', value=result)
        
        logging.info(f"✅ Extração NewCon Propostas: {len(result['newcon_proposals'])} registros")
        return result
    except Exception as e:
        logging.error(f"Erro na extração NewCon Propostas: {e}")
        raise

def airflow_extract_rdstation_leads_task(**context) -> Dict[str, Any]:
    """Task de extração específica para leads RD Station"""
    extractor = DataExtractor()
    
    try:
        result = {'rdstation_leads': extractor.extract_rdstation_leads()}
        
        if 'ti' in context:
            context['ti'].xcom_push(key='rdstation_leads_result', value=result)
        
        logging.info(f"✅ Extração RD Station Leads: {len(result['rdstation_leads'])} registros")
        return result
    except Exception as e:
        logging.error(f"Erro na extração RD Station Leads: {e}")
        raise

# =============================================================================
# FUNÇÕES DE EXTRAÇÃO PARALELA POR TABELA INDIVIDUAL
# =============================================================================

def airflow_extract_newcon_clients_individual_task(**context) -> None:
    """Task de extração individual para clientes NewCon"""
    extractor = DataExtractor()
    
    try:
        clients_df = extractor.extract_newcon_clients()
        result = {
            'success': True,
            'data': {'newcon_clients': clients_df},
            'total_records': len(clients_df),
            'table_name': 'newcon_clients',
            'source': 'newcon'
        }
        
        if 'ti' in context:
            context['ti'].xcom_push(key='newcon_clients_individual', value=result)
        
        logging.info(f"✅ Extração individual NewCon Clientes: {len(clients_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual NewCon Clientes: {e}")
        raise

def airflow_extract_newcon_products_individual_task(**context) -> None:
    """Task de extração individual para produtos NewCon"""
    extractor = DataExtractor()
    
    try:
        products_df = extractor.extract_newcon_products()
        result = {
            'success': True,
            'data': {'newcon_products': products_df},
            'total_records': len(products_df),
            'table_name': 'newcon_products',
            'source': 'newcon'
        }
        
        if 'ti' in context:
            context['ti'].xcom_push(key='newcon_products_individual', value=result)
        
        logging.info(f"✅ Extração individual NewCon Produtos: {len(products_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual NewCon Produtos: {e}")
        raise

def airflow_extract_newcon_leads_individual_task(**context) -> None:
    """Task de extração individual para leads NewCon"""
    extractor = DataExtractor()
    
    try:
        leads_df = extractor.extract_newcon_leads()
        result = {
            'success': True,
            'data': {'newcon_leads': leads_df},
            'total_records': len(leads_df),
            'table_name': 'newcon_leads',
            'source': 'newcon'
        }
        
        if 'ti' in context:
            context['ti'].xcom_push(key='newcon_leads_individual', value=result)
        
        logging.info(f"✅ Extração individual NewCon Leads: {len(leads_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual NewCon Leads: {e}")
        raise

def airflow_extract_newcon_proposals_individual_task(**context) -> None:
    """Task de extração individual para propostas NewCon"""
    extractor = DataExtractor()
    
    try:
        proposals_df = extractor.extract_newcon_proposals()
        result = {
            'success': True,
            'data': {'newcon_proposals': proposals_df},
            'total_records': len(proposals_df),
            'table_name': 'newcon_proposals',
            'source': 'newcon'
        }
        
        if 'ti' in context:
            context['ti'].xcom_push(key='newcon_proposals_individual', value=result)
        
        logging.info(f"✅ Extração individual NewCon Propostas: {len(proposals_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual NewCon Propostas: {e}")
        raise

def airflow_extract_rdstation_leads_individual_task(**context) -> None:
    """Task de extração individual para leads RD Station"""
    extractor = DataExtractor()
    
    try:
        leads_df = extractor.extract_rdstation_leads()
        result = {
            'success': True,
            'data': {'rdstation_leads': leads_df},
            'total_records': len(leads_df),
            'table_name': 'rdstation_leads',
            'source': 'rdstation'
        }
        
        if 'ti' in context:
            context['ti'].xcom_push(key='rdstation_leads_individual', value=result)
        
        logging.info(f"✅ Extração individual RD Station Leads: {len(leads_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual RD Station Leads: {e}")
        raise

def airflow_extract_orbbits_origin_individual_task(**context) -> None:
    """Task de extração individual para origin Orbbits"""
    extractor = DataExtractor()
    
    try:
        origin_df = extractor.extract_orbbits_origin()
        result = {
            'success': True,
            'data': {'orbbits_origin': origin_df},
            'total_records': len(origin_df),
            'table_name': 'orbbits_origin',
            'source': 'orbbits'
        }
        
        if 'ti' in context:
            context['ti'].xcom_push(key='orbbits_origin_individual', value=result)
        
        logging.info(f"✅ Extração individual Orbbits Origin: {len(origin_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual Orbbits Origin: {e}")
        raise

def airflow_extract_orbbits_payments_individual_task(**context) -> None:
    """Task de extração individual para payments Orbbits"""
    extractor = DataExtractor()
    
    try:
        payments_df = extractor.extract_orbbits_payments()
        result = {
            'success': True,
            'data': {'orbbits_payments': payments_df},
            'total_records': len(payments_df),
            'table_name': 'orbbits_payments',
            'source': 'orbbits'
        }
        
        if 'ti' in context:
            context['ti'].xcom_push(key='orbbits_payments_individual', value=result)
        
        logging.info(f"✅ Extração individual Orbbits Payments: {len(payments_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual Orbbits Payments: {e}")
        raise

def airflow_extract_orbbits_sales_individual_task(**context) -> None:
    """Task de extração individual para sales Orbbits"""
    extractor = DataExtractor()
    
    try:
        sales_df = extractor.extract_orbbits_sales()
        result = {
            'success': True,
            'data': {'orbbits_sales': sales_df},
            'total_records': len(sales_df),
            'table_name': 'orbbits_sales',
            'source': 'orbbits'
        }
        
        if 'ti' in context:
            context['ti'].xcom_push(key='orbbits_sales_individual', value=result)
        
        logging.info(f"✅ Extração individual Orbbits Sales: {len(sales_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual Orbbits Sales: {e}")
        raise

def airflow_extract_orbbits_prices_individual_task(**context) -> None:
    """Task de extração individual para prices Orbbits"""
    extractor = DataExtractor()

    try:
        prices_df = extractor.extract_orbbits_prices()
        result = {
            'success': True,
            'data': {'orbbits_prices': prices_df},
            'total_records': len(prices_df),
            'table_name': 'orbbits_prices',
            'source': 'orbbits'
        }

        if 'ti' in context:
            context['ti'].xcom_push(key='orbbits_prices_individual', value=result)

        logging.info(f"✅ Extração individual Orbbits Prices: {len(prices_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual Orbbits Prices: {e}")
        raise

def airflow_extract_orbbits_proposals_individual_task(**context) -> None:
    """Task de extração individual para proposals Orbbits"""
    extractor = DataExtractor()

    try:
        proposals_df = extractor.extract_orbbits_proposals()
        result = {
            'success': True,
            'data': {'orbbits_proposals': proposals_df},
            'total_records': len(proposals_df),
            'table_name': 'orbbits_proposals',
            'source': 'orbbits'
        }

        if 'ti' in context:
            context['ti'].xcom_push(key='orbbits_proposals_individual', value=result)

        logging.info(f"✅ Extração individual Orbbits Proposals: {len(proposals_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual Orbbits Proposals: {e}")
        raise

def airflow_extract_quiver_clients_individual_task(**context) -> None:
    """Task de extração individual para clientes Quiver"""
    extractor = DataExtractor()

    try:
        clients_df = extractor.extract_quiver_clients()
        result = {
            'success': True,
            'data': {'quiver_clients': clients_df},
            'total_records': len(clients_df),
            'table_name': 'quiver_clients',
            'source': 'quiver'
        }

        if 'ti' in context:
            context['ti'].xcom_push(key='quiver_clients_individual', value=result)

        logging.info(f"✅ Extração individual Quiver Clientes: {len(clients_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual Quiver Clientes: {e}")
        raise

def airflow_extract_quiver_leads_individual_task(**context) -> None:
    """Task de extração individual para leads Quiver"""
    extractor = DataExtractor()

    try:
        leads_df = extractor.extract_quiver_leads()
        result = {
            'success': True,
            'data': {'quiver_leads': leads_df},
            'total_records': len(leads_df),
            'table_name': 'quiver_leads',
            'source': 'quiver'
        }

        if 'ti' in context:
            context['ti'].xcom_push(key='quiver_leads_individual', value=result)

        logging.info(f"✅ Extração individual Quiver Leads: {len(leads_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual Quiver Leads: {e}")
        raise

def airflow_extract_quiver_products_individual_task(**context) -> None:
    """Task de extração individual para produtos Quiver"""
    extractor = DataExtractor()

    try:
        products_df = extractor.extract_quiver_products()
        result = {
            'success': True,
            'data': {'quiver_products': products_df},
            'total_records': len(products_df),
            'table_name': 'quiver_products',
            'source': 'quiver'
        }

        if 'ti' in context:
            context['ti'].xcom_push(key='quiver_products_individual', value=result)

        logging.info(f"✅ Extração individual Quiver Produtos: {len(products_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual Quiver Produtos: {e}")
        raise

def airflow_extract_quiver_proposals_individual_task(**context) -> None:
    """Task de extração individual para propostas Quiver"""
    extractor = DataExtractor()

    try:
        proposals_df = extractor.extract_quiver_proposals()
        result = {
            'success': True,
            'data': {'quiver_proposals': proposals_df},
            'total_records': len(proposals_df),
            'table_name': 'quiver_proposals',
            'source': 'quiver'
        }

        if 'ti' in context:
            context['ti'].xcom_push(key='quiver_proposals_individual', value=result)

        logging.info(f"✅ Extração individual Quiver Propostas: {len(proposals_df)} registros")
        # Não retorna nada para evitar return_value desnecessário
    except Exception as e:
        logging.error(f"Erro na extração individual Quiver Propostas: {e}")
        raise

# =============================================================================
# FUNÇÃO DE CONSOLIDAÇÃO DE EXTRAÇÕES PARALELAS POR TABELA
# =============================================================================

def airflow_consolidate_table_extractions_task(**context) -> None:
    """Task para consolidar resultados de extrações paralelas por tabela"""
    try:
        consolidated_data = {}
        
        # Lista de todas as tabelas extraídas individualmente
        table_keys = [
            'newcon_clients_individual',
            'newcon_products_individual',
            'newcon_leads_individual',
            'newcon_proposals_individual',
            'rdstation_leads_individual',
            'orbbits_origin_individual',
            'orbbits_payments_individual',
            'orbbits_sales_individual',
            'orbbits_prices_individual',
            'orbbits_proposals_individual',
            'quiver_clients_individual',
            'quiver_leads_individual',
            'quiver_products_individual',
            'quiver_proposals_individual'
        ]
        
        # Recupera dados de todas as extrações paralelas
        if 'ti' in context:
            ti = context['ti']
            
            for key in table_keys:
                table_data = ti.xcom_pull(key=key)
                if table_data and table_data.get('success', False):
                    consolidated_data.update(table_data['data'])
                    logging.info(f"✅ Consolidado {key}: {table_data['total_records']} registros")
                else:
                    logging.warning(f"⚠️ Dados não encontrados para {key}")
            
            # Salva dados consolidados
            consolidation_result = {
                'success': True,
                'data': consolidated_data,
                'total_records': sum(len(df) for df in consolidated_data.values() if hasattr(df, '__len__')),
                'tables_count': len(consolidated_data),
                'extraction_method': 'table_parallel'
            }
            
            ti.xcom_push(key='consolidated_table_extraction_result', value=consolidation_result)
            
            logging.info(f"✅ Consolidação por tabela concluída: {consolidation_result['total_records']} registros de {consolidation_result['tables_count']} tabelas")
            # Não retorna nada para evitar return_value desnecessário
            
        else:
            raise ValueError("TaskInstance não encontrada")
    except Exception as e:
        logging.error(f"Erro na consolidação de extrações por tabela: {e}")
        raise

# =============================================================================
# FUNÇÃO DE CONSOLIDAÇÃO DE EXTRAÇÕES PARALELAS POR FONTE
# =============================================================================

def airflow_consolidate_extractions_task(**context) -> Dict[str, Any]:
    """Task para consolidar resultados de extrações paralelas"""
    try:
        consolidated_data = {}
        
        # Recupera dados de todas as extrações paralelas
        if 'ti' in context:
            ti = context['ti']
            
            # Recupera dados NewCon
            newcon_data = ti.xcom_pull(key='newcon_extraction_result') or {}
            consolidated_data.update(newcon_data)
            
            # Recupera dados RD Station
            rdstation_data = ti.xcom_pull(key='rdstation_extraction_result') or {}
            consolidated_data.update(rdstation_data)
            
            # Recupera dados Orbbits
            orbbits_data = ti.xcom_pull(key='orbbits_extraction_result') or {}
            consolidated_data.update(orbbits_data)
            
            # Salva dados consolidados
            consolidation_result = {
                'success': True,
                'data': consolidated_data,
                'total_records': sum(len(df) for df in consolidated_data.values()),
                'sources_count': len(consolidated_data)
            }
            
            ti.xcom_push(key='consolidated_extraction_result', value=consolidation_result)
            
            logging.info(f"✅ Consolidação concluída: {consolidation_result['total_records']} registros de {consolidation_result['sources_count']} fontes")
            return consolidation_result
            
        else:
            raise ValueError("TaskInstance não encontrada")
    except Exception as e:
        logging.error(f"Erro na consolidação de extrações: {e}")
        raise

def airflow_transform_task(table_name: str = None, **context) -> Dict[str, Any]:
    """Task de transformação para Airflow"""
    try:
        # Recupera dados da extração
        if 'ti' in context:
            raw_data = context['ti'].xcom_pull(key='extraction_result')
        else:
            raise ValueError("Dados de extração não encontrados")
        
        # Transforma dados
        result = transform_all_data(raw_data['data'])
        
        # Salva resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='transformation_result', value=result)
        
        return result
    except Exception as e:
        logging.error(f"Erro na task de transformação: {e}")
        raise

# =============================================================================
# FUNÇÕES DE TRANSFORMAÇÃO PARALELA POR TABELA
# =============================================================================

def airflow_transform_parallel_task(**context) -> Dict[str, Any]:
    """Task de transformação paralela baseada em dados consolidados"""
    try:
        # Recupera dados consolidados da extração
        if 'ti' in context:
            consolidated_data = context['ti'].xcom_pull(key='consolidated_extraction_result')
        else:
            raise ValueError("Dados consolidados não encontrados")
        
        # Transforma dados
        result = transform_all_data(consolidated_data['data'])
        
        # Salva resultado no XCom
        if 'ti' in context:
            context['ti'].xcom_push(key='parallel_transformation_result', value=result)
        
        return result
    except Exception as e:
        logging.error(f"Erro na task de transformação paralela: {e}")
        raise

def airflow_transform_produtos_task(**context) -> None:
    """Task de transformação específica para produtos"""
    try:
        # Recupera dados consolidados
        if 'ti' in context:
            consolidated_data = context['ti'].xcom_pull(key='consolidated_table_extraction_result')
        else:
            raise ValueError("Dados consolidados não encontrados")
        # Passa todos os dados consolidados para a transformação
        all_data = consolidated_data['data']
        # Transforma dados usando função específica para produtos
        if all_data:
            transformed_tables = transform_produtos_only(all_data)
            tb_produtos = transformed_tables.get('tb_produtos', pd.DataFrame())

            # Aplicar preparação adicional para serialização se necessário
            from salesforce_integration.data_transformers import _prepare_dataframe_for_serialization
            tb_produtos = _prepare_dataframe_for_serialization(tb_produtos)

            products_result = {
                'success': True,
                'transformed_data': {'tb_produtos': tb_produtos},
                'total_records': len(tb_produtos) if hasattr(tb_produtos, '__len__') else 0,
                'table_name': 'tb_produtos'
            }
        else:
            products_result = {
                'success': True,
                'transformed_data': {'tb_produtos': pd.DataFrame()},
                'total_records': 0,
                'table_name': 'tb_produtos'
            }
        if 'ti' in context:
            context['ti'].xcom_push(key='produtos_transformation_result', value=products_result)
        logging.info(f"✅ Transformação Produtos: {products_result['total_records']} registros")
    except Exception as e:
        logging.error(f"Erro na transformação de produtos: {e}")
        raise

def airflow_transform_clientes_task(**context) -> None:
    """Task de transformação específica para clientes"""
    try:
        if 'ti' in context:
            consolidated_data = context['ti'].xcom_pull(key='consolidated_table_extraction_result')
        else:
            raise ValueError("Dados consolidados não encontrados")
        if consolidated_data is None:
            logging.error("❌ Dados consolidados não encontrados no XCom")
            raise ValueError("Dados consolidados não encontrados no XCom")
        all_data = consolidated_data.get('data', {})
        if all_data:
            transformed_tables = transform_clientes_only(all_data)
            tb_clientes = transformed_tables.get('tb_clientes', pd.DataFrame())

            # Aplicar preparação adicional para serialização se necessário
            from salesforce_integration.data_transformers import _prepare_dataframe_for_serialization
            tb_clientes = _prepare_dataframe_for_serialization(tb_clientes)

            clients_result = {
                'success': True,
                'transformed_data': {'tb_clientes': tb_clientes},
                'total_records': len(tb_clientes) if hasattr(tb_clientes, '__len__') else 0,
                'table_name': 'tb_clientes'
            }
        else:
            clients_result = {
                'success': True,
                'transformed_data': {'tb_clientes': pd.DataFrame()},
                'total_records': 0,
                'table_name': 'tb_clientes'
            }
        if 'ti' in context:
            context['ti'].xcom_push(key='clientes_transformation_result', value=clients_result)
        logging.info(f"✅ Transformação Clientes: {clients_result['total_records']} registros")
    except Exception as e:
        logging.error(f"Erro na transformação de clientes: {e}")
        raise

def airflow_transform_leads_task(**context) -> None:
    """Task de transformação específica para leads"""
    try:
        if 'ti' in context:
            consolidated_data = context['ti'].xcom_pull(key='consolidated_table_extraction_result')
        else:
            raise ValueError("Dados consolidados não encontrados")
        all_data = consolidated_data['data']
        if all_data:
            transformed_tables = transform_leads_only(all_data)
            tb_leads = transformed_tables.get('tb_leads', pd.DataFrame())

            # Aplicar preparação adicional para serialização se necessário
            from salesforce_integration.data_transformers import _prepare_dataframe_for_serialization
            tb_leads = _prepare_dataframe_for_serialization(tb_leads)

            leads_result = {
                'success': True,
                'transformed_data': {'tb_leads': tb_leads},
                'total_records': len(tb_leads) if hasattr(tb_leads, '__len__') else 0,
                'table_name': 'tb_leads'
            }
        else:
            leads_result = {
                'success': True,
                'transformed_data': {'tb_leads': pd.DataFrame()},
                'total_records': 0,
                'table_name': 'tb_leads'
            }
        if 'ti' in context:
            context['ti'].xcom_push(key='leads_transformation_result', value=leads_result)
        logging.info(f"✅ Transformação Leads: {leads_result['total_records']} registros")
    except Exception as e:
        logging.error(f"Erro na transformação de leads: {e}")
        raise

def airflow_transform_propostas_task(**context) -> None:
    """Task de transformação específica para propostas"""
    try:
        if 'ti' in context:
            consolidated_data = context['ti'].xcom_pull(key='consolidated_table_extraction_result')
        else:
            raise ValueError("Dados consolidados não encontrados")
        all_data = consolidated_data['data']
        if all_data:
            transformed_tables = transform_propostas_only(all_data)
            tb_propostas = transformed_tables.get('tb_propostas', pd.DataFrame())

            # Aplicar preparação adicional para serialização se necessário
            from salesforce_integration.data_transformers import _prepare_dataframe_for_serialization
            tb_propostas = _prepare_dataframe_for_serialization(tb_propostas)

            proposals_result = {
                'success': True,
                'transformed_data': {'tb_propostas': tb_propostas},
                'total_records': len(tb_propostas) if hasattr(tb_propostas, '__len__') else 0,
                'table_name': 'tb_propostas'
            }
        else:
            proposals_result = {
                'success': True,
                'transformed_data': {'tb_propostas': pd.DataFrame()},
                'total_records': 0,
                'table_name': 'tb_propostas'
            }
        if 'ti' in context:
            context['ti'].xcom_push(key='propostas_transformation_result', value=proposals_result)
        logging.info(f"✅ Transformação Propostas: {proposals_result['total_records']} registros")
    except Exception as e:
        logging.error(f"Erro na transformação de propostas: {e}")
        raise

def airflow_load_task(table_name: str = None, **context) -> Dict[str, Any]:
    """Task de carregamento para Airflow"""
    try:
        # Recupera dados da transformação
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='transformation_result')
        else:
            raise ValueError("Dados de transformação não encontrados")
        
        # Carrega dados
        tables_data = transformed_data['transformed_data']
        
        if table_name:
            # Carrega apenas tabela específica
            if table_name in tables_data:
                single_table_data = {table_name: tables_data[table_name]}
                result = process_etl_pipeline(single_table_data, dry_run=False)
            else:
                raise ValueError(f"Tabela '{table_name}' não encontrada")
        else:
            # Carrega todas as tabelas
            result = process_etl_pipeline(tables_data, dry_run=False)
        
        return result
    except Exception as e:
        logging.error(f"Erro na task de carregamento: {e}")
        raise

# =============================================================================
# FUNÇÕES DE CARREGAMENTO PARALELO POR TABELA
# =============================================================================

def airflow_load_produtos_parallel_task(**context) -> Dict[str, Any]:
    """Task de carregamento paralelo para produtos"""
    try:
        # Recupera dados da transformação de produtos
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='produtos_transformation_result')
        else:
            raise ValueError("Dados de transformação de produtos não encontrados")
        
        # Carrega dados de produtos
        if transformed_data['success'] and not transformed_data['transformed_data']['tb_produtos'].empty:
            result = process_etl_pipeline(transformed_data['transformed_data'], dry_run=False)
            logging.info(f"✅ Carregamento Produtos: {result.get('total_records', 0)} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_produtos'],
                'message': 'Nenhum dado de produtos para carregar'
            }
            logging.info("⚠️ Carregamento Produtos: Nenhum dado para carregar")
        
        return result
    except Exception as e:
        logging.error(f"Erro no carregamento de produtos: {e}")
        raise

def airflow_load_clientes_parallel_task(**context) -> Dict[str, Any]:
    """Task de carregamento paralelo para clientes"""
    try:
        # Recupera dados da transformação de clientes
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='clientes_transformation_result')
        else:
            raise ValueError("Dados de transformação de clientes não encontrados")

        # Verifica se os dados foram encontrados
        if transformed_data is None:
            logging.error("❌ Dados de transformação de clientes não encontrados no XCom")
            raise ValueError("Dados de transformação de clientes não encontrados no XCom")

        # Carrega dados de clientes
        if transformed_data.get('success', False) and 'transformed_data' in transformed_data and 'tb_clientes' in transformed_data['transformed_data'] and not transformed_data['transformed_data']['tb_clientes'].empty:
            result = process_etl_pipeline(transformed_data['transformed_data'], dry_run=False)
            logging.info(f"✅ Carregamento Clientes: {result.get('total_records', 0)} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_clientes'],
                'message': 'Nenhum dado de clientes para carregar'
            }
            logging.info("⚠️ Carregamento Clientes: Nenhum dado para carregar")
        
        return result
    except Exception as e:
        logging.error(f"Erro no carregamento de clientes: {e}")
        raise

def airflow_load_leads_parallel_task(**context) -> Dict[str, Any]:
    """Task de carregamento paralelo para leads"""
    try:
        # Recupera dados da transformação de leads
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='leads_transformation_result')
        else:
            raise ValueError("Dados de transformação de leads não encontrados")

        # Verifica se os dados foram encontrados
        if transformed_data is None:
            logging.error("❌ Dados de transformação de leads não encontrados no XCom")
            raise ValueError("Dados de transformação de leads não encontrados no XCom")

        # Carrega dados de leads
        if transformed_data.get('success', False) and 'transformed_data' in transformed_data and 'tb_leads' in transformed_data['transformed_data'] and not transformed_data['transformed_data']['tb_leads'].empty:
            result = process_etl_pipeline(transformed_data['transformed_data'], dry_run=False)
            logging.info(f"✅ Carregamento Leads: {result.get('total_records', 0)} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_leads'],
                'message': 'Nenhum dado de leads para carregar'
            }
            logging.info("⚠️ Carregamento Leads: Nenhum dado para carregar")
        
        return result
    except Exception as e:
        logging.error(f"Erro no carregamento de leads: {e}")
        raise

def airflow_load_propostas_parallel_task(**context) -> Dict[str, Any]:
    """Task de carregamento paralelo para propostas"""
    try:
        # Recupera dados da transformação de propostas
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='propostas_transformation_result')
        else:
            raise ValueError("Dados de transformação de propostas não encontrados")
        
        # Carrega dados de propostas
        if transformed_data['success'] and not transformed_data['transformed_data']['tb_propostas'].empty:
            result = process_etl_pipeline(transformed_data['transformed_data'], dry_run=False)
            logging.info(f"✅ Carregamento Propostas: {result.get('total_records', 0)} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_propostas'],
                'message': 'Nenhum dado de propostas para carregar'
            }
            logging.info("⚠️ Carregamento Propostas: Nenhum dado para carregar")
        
        return result
    except Exception as e:
        logging.error(f"Erro no carregamento de propostas: {e}")
        raise

# =============================================================================
# FUNÇÕES DE EXPORTAÇÃO CSV
# =============================================================================

def export_to_csv(data: pd.DataFrame, table_name: str, output_dir: str = None) -> str:
    """Exporta dados para CSV, removendo arquivos antigos da mesma tabela"""
    try:
        # Define diretório padrão se não especificado
        if output_dir is None:
            # Usa o diretório da DAG + /carga_fria como padrão
            current_dir = os.path.dirname(os.path.abspath(__file__))
            output_dir = os.path.join(current_dir, 'carga_fria')
        
        # Cria diretório se não existir
        os.makedirs(output_dir, exist_ok=True)
        
        # Remove arquivos antigos da mesma tabela
        old_files_pattern = os.path.join(output_dir, f"{table_name}_*.csv")
        old_files = glob.glob(old_files_pattern)
        
        for old_file in old_files:
            try:
                os.remove(old_file)
                logging.info(f"🗑️ Arquivo antigo removido: {os.path.basename(old_file)}")
            except Exception as e:
                logging.warning(f"⚠️ Não foi possível remover arquivo antigo {old_file}: {e}")
        
        # Gera nome do arquivo com timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{table_name}_{timestamp}.csv"
        filepath = os.path.join(output_dir, filename)
        
        # Exporta para CSV
        data.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        logging.info(f"✅ CSV exportado: {filepath} ({len(data)} registros)")
        return filepath
        
    except Exception as e:
        logging.error(f"Erro ao exportar CSV para {table_name}: {e}")
        raise

def airflow_export_produtos_csv_task(**context) -> Dict[str, Any]:
    """Task de exportação CSV para produtos"""
    try:
        # Recupera dados da transformação de produtos
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='produtos_transformation_result')
        else:
            raise ValueError("Dados de transformação de produtos não encontrados")
        
        # Obtém o diretório CSV configurado da DAG
        dag_context = context.get('dag')
        output_dir = None
        if dag_context:
            # Obtém o diretório da DAG
            dag_dir = os.path.dirname(os.path.abspath(dag_context.fileloc))
            output_dir = os.path.join(dag_dir, 'carga_fria')
        
        # Exporta dados para CSV
        if transformed_data['success'] and not transformed_data['transformed_data']['tb_produtos'].empty:
            filepath = export_to_csv(
                transformed_data['transformed_data']['tb_produtos'],
                'tb_produtos',
                output_dir
            )
            result = {
                'success': True,
                'total_records': len(transformed_data['transformed_data']['tb_produtos']),
                'tables_processed': ['tb_produtos'],
                'csv_file': filepath,
                'message': f'Dados exportados para CSV: {filepath}'
            }
            logging.info(f"✅ Exportação CSV Produtos: {result['total_records']} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_produtos'],
                'csv_file': None,
                'message': 'Nenhum dado de produtos para exportar'
            }
            logging.info("⚠️ Exportação CSV Produtos: Nenhum dado para exportar")
        
        return result
    except Exception as e:
        logging.error(f"Erro na exportação CSV de produtos: {e}")
        raise

def airflow_export_clientes_csv_task(**context) -> Dict[str, Any]:
    """Task de exportação CSV para clientes"""
    try:
        # Recupera dados da transformação de clientes
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='clientes_transformation_result')
        else:
            raise ValueError("Dados de transformação de clientes não encontrados")
        
        # Obtém o diretório CSV configurado da DAG
        dag_context = context.get('dag')
        output_dir = None
        if dag_context:
            # Obtém o diretório da DAG
            dag_dir = os.path.dirname(os.path.abspath(dag_context.fileloc))
            output_dir = os.path.join(dag_dir, 'carga_fria')
        
        # Exporta dados para CSV
        if transformed_data['success'] and not transformed_data['transformed_data']['tb_clientes'].empty:
            filepath = export_to_csv(
                transformed_data['transformed_data']['tb_clientes'],
                'tb_clientes',
                output_dir
            )
            result = {
                'success': True,
                'total_records': len(transformed_data['transformed_data']['tb_clientes']),
                'tables_processed': ['tb_clientes'],
                'csv_file': filepath,
                'message': f'Dados exportados para CSV: {filepath}'
            }
            logging.info(f"✅ Exportação CSV Clientes: {result['total_records']} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_clientes'],
                'csv_file': None,
                'message': 'Nenhum dado de clientes para exportar'
            }
            logging.info("⚠️ Exportação CSV Clientes: Nenhum dado para exportar")
        
        return result
    except Exception as e:
        logging.error(f"Erro na exportação CSV de clientes: {e}")
        raise

def airflow_export_leads_csv_task(**context) -> Dict[str, Any]:
    """Task de exportação CSV para leads"""
    try:
        # Recupera dados da transformação de leads
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='leads_transformation_result')
        else:
            raise ValueError("Dados de transformação de leads não encontrados")
        
        # Obtém o diretório CSV configurado da DAG
        dag_context = context.get('dag')
        output_dir = None
        if dag_context:
            # Obtém o diretório da DAG
            dag_dir = os.path.dirname(os.path.abspath(dag_context.fileloc))
            output_dir = os.path.join(dag_dir, 'carga_fria')
        
        # Exporta dados para CSV
        if transformed_data['success'] and not transformed_data['transformed_data']['tb_leads'].empty:
            filepath = export_to_csv(
                transformed_data['transformed_data']['tb_leads'],
                'tb_leads',
                output_dir
            )
            result = {
                'success': True,
                'total_records': len(transformed_data['transformed_data']['tb_leads']),
                'tables_processed': ['tb_leads'],
                'csv_file': filepath,
                'message': f'Dados exportados para CSV: {filepath}'
            }
            logging.info(f"✅ Exportação CSV Leads: {result['total_records']} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_leads'],
                'csv_file': None,
                'message': 'Nenhum dado de leads para exportar'
            }
            logging.info("⚠️ Exportação CSV Leads: Nenhum dado para exportar")
        
        return result
    except Exception as e:
        logging.error(f"Erro na exportação CSV de leads: {e}")
        raise

def airflow_export_propostas_csv_task(**context) -> Dict[str, Any]:
    """Task de exportação CSV para propostas"""
    try:
        # Recupera dados da transformação de propostas
        if 'ti' in context:
            transformed_data = context['ti'].xcom_pull(key='propostas_transformation_result')
        else:
            raise ValueError("Dados de transformação de propostas não encontrados")
        
        # Obtém o diretório CSV configurado da DAG
        dag_context = context.get('dag')
        output_dir = None
        if dag_context:
            # Obtém o diretório da DAG
            dag_dir = os.path.dirname(os.path.abspath(dag_context.fileloc))
            output_dir = os.path.join(dag_dir, 'carga_fria')
        
        # Exporta dados para CSV
        if transformed_data['success'] and not transformed_data['transformed_data']['tb_propostas'].empty:
            filepath = export_to_csv(
                transformed_data['transformed_data']['tb_propostas'],
                'tb_propostas',
                output_dir
            )
            result = {
                'success': True,
                'total_records': len(transformed_data['transformed_data']['tb_propostas']),
                'tables_processed': ['tb_propostas'],
                'csv_file': filepath,
                'message': f'Dados exportados para CSV: {filepath}'
            }
            logging.info(f"✅ Exportação CSV Propostas: {result['total_records']} registros")
        else:
            result = {
                'success': True,
                'total_records': 0,
                'tables_processed': ['tb_propostas'],
                'csv_file': None,
                'message': 'Nenhum dado de propostas para exportar'
            }
            logging.info("⚠️ Exportação CSV Propostas: Nenhum dado para exportar")
        
        return result
    except Exception as e:
        logging.error(f"Erro na exportação CSV de propostas: {e}")
        raise

# =============================================================================
# FUNÇÕES AUXILIARES
# =============================================================================

def create_dag_template() -> str:
    """Cria template de DAG para Airflow"""
    template = '''
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from salesforce_integration.etl_main import airflow_extract_task, airflow_transform_task, airflow_load_task

default_args = {
    'owner': 'etl_team',
    'depends_on_past': False,
    'start_date': datetime(2025, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'etl_salesforce_marketing_cloud',
    default_args=default_args,
    description='ETL Pipeline para Salesforce Marketing Cloud',
    schedule_interval='0 8 * * *',  # Diário às 8h
    catchup=False,
    max_active_runs=1,
)

# Task de extração
extract_task = PythonOperator(
    task_id='extract_data',
    python_callable=airflow_extract_task,
    dag=dag,
)

# Task de transformação
transform_task = PythonOperator(
    task_id='transform_data',
    python_callable=airflow_transform_task,
    dag=dag,
)

# Tasks de carregamento por tabela
load_products_task = PythonOperator(
    task_id='load_products',
    python_callable=airflow_load_task,
    op_args=['tb_produtos'],
    dag=dag,
)

load_clients_task = PythonOperator(
    task_id='load_clients',
    python_callable=airflow_load_task,
    op_args=['tb_clientes'],
    dag=dag,
)

load_leads_task = PythonOperator(
    task_id='load_leads',
    python_callable=airflow_load_task,
    op_args=['tb_leads'],
    dag=dag,
)

load_proposals_task = PythonOperator(
    task_id='load_proposals',
    python_callable=airflow_load_task,
    op_args=['tb_propostas'],
    dag=dag,
)

# Sequência de dependências
extract_task >> transform_task >> [
    load_products_task,
    load_clients_task,
    load_leads_task,
    load_proposals_task
]
'''
    return template

def generate_execution_report(pipeline_result: Dict[str, Any]) -> str:
    """Gera relatório de execução com sistema de falhas integrado"""
    if not pipeline_result['success']:
        return f"❌ PIPELINE FALHOU: {pipeline_result.get('error', 'Erro desconhecido')}"
    
    # Prioriza o relatório de falhas se disponível
    if 'failure_report' in pipeline_result and pipeline_result['failure_report'].strip():
        return pipeline_result['failure_report']
    
    # Fallback para relatório simples se não houver falhas detectadas
    stats = pipeline_result['execution_stats']
    load_result = pipeline_result['load_result']
    
    report = f"""
===== RELATÓRIO DE EXECUÇÃO ETL =====
📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | ⏱️ Duração: {stats['total_time']/60:.0f}min | 📊 Status: ✅ SUCESSO

📊 RESUMO EXECUTIVO
"""
    
    for table_name, table_result in load_result.get('processing_summary', {}).items():
        status_icon = "✅" if table_result.get('success', True) else "❌"
        report += f"├─ {table_name}: {status_icon} {table_result.get('total_records', 0):,} registros | {table_result.get('total_batches', 0)} lotes\n"
    
    report += f"\n🎯 RESULTADO: Pipeline executado com sucesso - {stats['total_records']:,} registros processados"
    
    return report

# =============================================================================
# LINHA DE COMANDO
# =============================================================================

def parse_arguments():
    """Parseia argumentos da linha de comando"""
    parser = argparse.ArgumentParser(
        description="ETL Pipeline para Salesforce Marketing Cloud",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:
  python etl_main.py                           # Executa pipeline completo
  python etl_main.py --dry-run                 # Executa em modo dry-run
  python etl_main.py --table tb_clientes       # Executa apenas clientes
  python etl_main.py --tables tb_produtos,tb_clientes  # Executa produtos e clientes
  python etl_main.py --generate-dag            # Gera template de DAG
        """
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Executa em modo dry-run (não envia dados para Salesforce)'
    )
    
    parser.add_argument(
        '--table',
        type=str,
        help='Executa pipeline para uma única tabela'
    )
    
    parser.add_argument(
        '--tables',
        type=str,
        help='Executa pipeline para tabelas específicas (separadas por vírgula)'
    )
    
    parser.add_argument(
        '--sequence',
        type=str,
        help='Sequência customizada de tabelas (separadas por vírgula)'
    )
    
    parser.add_argument(
        '--generate-dag',
        action='store_true',
        help='Gera template de DAG para Airflow'
    )
    
    parser.add_argument(
        '--test-connections',
        action='store_true',
        help='Testa apenas as conexões'
    )

    parser.add_argument(
        '--test-sample',
        type=int,
        default=None,
        help='Executa com amostra limitada de registros (ex: --test-sample 100)'
    )

    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Nível de log'
    )
    
    return parser.parse_args()

def main():
    """Função principal"""
    args = parse_arguments()
    
    # Configura nível de log
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Gera template DAG
    if args.generate_dag:
        template = create_dag_template()
        print(template)
        return 0
    
    # Testa apenas conexões
    if args.test_connections:
        print("Testando conexões...")
        if test_all_connections() and test_salesforce_connection():
            print("✅ Todas as conexões OK")
            return 0
        else:
            print("❌ Falha nas conexões")
            return 1
    
    # Configura filtros de tabela
    table_filter = None
    if args.table:
        table_filter = [args.table]
    elif args.tables:
        table_filter = [t.strip() for t in args.tables.split(',')]
    
    # Configura sequência
    table_sequence = None
    if args.sequence:
        table_sequence = [t.strip() for t in args.sequence.split(',')]
    
    # Executa pipeline
    pipeline = ETLPipeline(dry_run=args.dry_run, test_sample=args.test_sample)
    
    try:
        result = pipeline.run_pipeline(
            table_filter=table_filter,
            table_sequence=table_sequence
        )
        
        # Gera relatório
        report = generate_execution_report(result)
        print(report)
        
        return 0 if result['success'] else 1
        
    except KeyboardInterrupt:
        logger.info("Pipeline interrompido pelo usuário")
        return 1
    except Exception as e:
        logger.error(f"Erro inesperado: {e}")
        return 1
    finally:
        pipeline.cleanup()

if __name__ == "__main__":
    sys.exit(main())